'use client';

import React, { useState, useMemo } from 'react';
import { 
  PlusIcon, 
  EyeIcon, 
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  TicketIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/lib/utils';
import { useCoupons, useDeleteCoupon, useToggleCouponStatus } from '@/hooks/useCoupons';
import { Coupon } from '@/types/api';
import CouponForm from '@/components/forms/CouponForm';
import ApiDebugger from '@/components/debug/ApiDebugger';



export default function CouponsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [page, setPage] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<Coupon | null>(null);

  // Prepare query parameters
  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      ordering: '-created_at',
      limit: 20,
      offset: (page - 1) * 20,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (statusFilter) {
      params.is_active = statusFilter === 'active';
    }

    if (typeFilter) {
      params.discount_type = typeFilter;
    }

    return params;
  }, [searchTerm, statusFilter, typeFilter, page]);

  // Use React Query hooks
  const { data: couponsResponse, isLoading, error } = useCoupons(queryParams);
  const deleteCoponMutation = useDeleteCoupon();
  const toggleStatusMutation = useToggleCouponStatus();

  const coupons = couponsResponse?.results || [];
  const totalItems = couponsResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle coupon actions
  const handleDeleteCoupon = (couponId: number) => {
    if (confirm('Are you sure you want to delete this coupon?')) {
      deleteCoponMutation.mutate(couponId);
    }
  };

  const handleToggleStatus = (coupon: Coupon) => {
    toggleStatusMutation.mutate({
      couponId: coupon.id,
      isActive: !coupon.is_active
    });
  };

  const handleEditCoupon = (coupon: Coupon) => {
    setEditingCoupon(coupon);
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingCoupon(null);
  };

  // Define table columns
  const columns: Column<Coupon>[] = [
    {
      key: 'code',
      label: 'Coupon',
      sortable: true,
      render: (_, coupon) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
              <TicketIcon className="w-4 h-4 text-blue-600" />
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900">{coupon.code}</div>
            <div className="text-sm text-gray-500">{coupon.name}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'discount_value',
      label: 'Discount',
      render: (_, coupon) => (
        <div>
          <div className="font-medium text-gray-900">
            {coupon.discount_type === 'percentage' 
              ? `${coupon.discount_value}%` 
              : formatCurrency(coupon.discount_value)
            }
          </div>
          <div className="text-sm text-gray-500">
            {coupon.discount_type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
          </div>
        </div>
      ),
    },
    {
      key: 'usage_limit',
      label: 'Usage',
      render: (_, coupon) => (
        <div>
          <div className="font-medium text-gray-900">
            {coupon.usage_limit || 'Unlimited'}
          </div>
          <div className="text-sm text-gray-500">
            Usage limit
          </div>
        </div>
      ),
    },
    {
      key: 'minimum_order_amount',
      label: 'Min Order',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {value ? formatCurrency(value) : 'No minimum'}
        </div>
      ),
    },
    {
      key: 'valid_until',
      label: 'Validity',
      render: (value, coupon) => {
        const isExpired = new Date(value) < new Date();
        return (
          <div>
            <div className={`text-sm ${isExpired ? 'text-red-600' : 'text-gray-900'}`}>
              {formatDateTime(value)}
            </div>
            <div className="text-xs text-gray-500">
              {isExpired ? 'Expired' : formatRelativeTime(value)}
            </div>
          </div>
        );
      },
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value, coupon) => {
        const isExpired = new Date(coupon.valid_until) < new Date();
        return (
          <Badge 
            variant={
              !value ? 'secondary' :
              isExpired ? 'danger' : 
              'success'
            }
          >
            {!value ? 'Inactive' : isExpired ? 'Expired' : 'Active'}
          </Badge>
        );
      },
    },
  ];

  // Define row actions
  const rowActions = (coupon: Coupon) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="Edit Coupon">
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="View Analytics">
        <ChartBarIcon className="w-4 h-4 text-blue-500" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleToggleStatus(coupon)}
        title={coupon.is_active ? 'Deactivate' : 'Activate'}
      >
        {coupon.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleDeleteCoupon(coupon.id)}
        title="Delete Coupon"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Coupon Management</h1>
          <Button>
            <PlusIcon className="w-4 h-4 mr-2" />
            Create Coupon
          </Button>
        </div>



        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Discount Type
              </label>
              <select
                value={typeFilter}
                onChange={(e) => {
                  setTypeFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Types</option>
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
              </select>
            </div>
          </div>
        </div>

        {/* Coupons Table */}
        <DataTable
          data={coupons}
          columns={columns}
          loading={isLoading}
          error={error ? (error as any).message : undefined}
          searchable
          searchPlaceholder="Search coupons by code or name..."
          onSearch={handleSearch}
          sortable
          pagination={{
            page,
            totalPages,
            totalItems,
            itemsPerPage: 20,
            onPageChange: setPage,
          }}
          actions={rowActions}
          emptyState={{
            title: 'No coupons found',
            description: 'No coupons match your current filters.',
            action: (
              <div className="space-y-3">
                <Button onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setTypeFilter('');
                  setPage(1);
                }}>
                  Clear Filters
                </Button>
                <div className="text-sm text-gray-500">or</div>
                <Button>
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create First Coupon
                </Button>
              </div>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
}
