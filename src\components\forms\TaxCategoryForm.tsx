'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, CalculatorIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select, Checkbox } from '@/components/ui/Form';
import { useCreateTaxCategory, useUpdateTaxCategory } from '@/hooks/useTaxes';
import { taxCategorySchema, TaxCategoryFormData } from '@/lib/validations';
import { TaxCategory } from '@/types/api';

interface TaxCategoryFormProps {
  taxCategory?: TaxCategory;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function TaxCategoryForm({ taxCategory, isOpen, onClose, onSuccess }: TaxCategoryFormProps) {
  const isEditing = !!taxCategory;
  const createTaxCategoryMutation = useCreateTaxCategory();
  const updateTaxCategoryMutation = useUpdateTaxCategory();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
  } = useForm<TaxCategoryFormData>({
    resolver: zodResolver(taxCategorySchema),
    defaultValues: {
      name: '',
      description: '',
      gst_rate: '',
      is_active: true,
    },
  });

  const gstRate = watch('gst_rate');

  // Reset form when taxCategory changes
  useEffect(() => {
    if (taxCategory) {
      reset({
        name: taxCategory.name,
        description: taxCategory.description || '',
        gst_rate: taxCategory.gst_rate.toString(),
        is_active: taxCategory.is_active,
      });
    } else {
      reset();
    }
  }, [taxCategory, reset]);

  const onSubmit = async (data: TaxCategoryFormData) => {
    try {
      const formattedData = {
        ...data,
        description: data.description || undefined,
      };

      if (isEditing && taxCategory) {
        await updateTaxCategoryMutation.mutateAsync({
          categoryId: taxCategory.id,
          data: formattedData,
        });
      } else {
        await createTaxCategoryMutation.mutateAsync(formattedData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                <CalculatorIcon className="w-5 h-5 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit Tax Category' : 'Create New Tax Category'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Tax Category Details</h3>
              
              <FormField
                label="Category Name"
                required
                error={errors.name}
              >
                <Input
                  {...register('name')}
                  placeholder="e.g., Standard Rate, Reduced Rate"
                  error={!!errors.name}
                />
              </FormField>

              <FormField
                label="Description"
                error={errors.description}
              >
                <Textarea
                  {...register('description')}
                  placeholder="Describe what services/products this tax category applies to"
                  rows={3}
                  error={!!errors.description}
                />
              </FormField>
            </div>

            {/* GST Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">GST Configuration</h3>
              
              <FormField
                label="GST Rate (%)"
                required
                error={errors.gst_rate}
              >
                <Select
                  {...register('gst_rate')}
                  options={[
                    { value: '', label: 'Select GST rate...' },
                    { value: '0', label: '0% - Exempt' },
                    { value: '5', label: '5% - Essential goods' },
                    { value: '12', label: '12% - Standard goods' },
                    { value: '18', label: '18% - Standard services' },
                    { value: '28', label: '28% - Luxury items' },
                  ]}
                  error={!!errors.gst_rate}
                />
              </FormField>

              {/* GST Rate Information */}
              {gstRate && (
                <div className={`p-4 rounded-lg border ${
                  gstRate === '0' 
                    ? 'bg-gray-50 border-gray-200' 
                    : gstRate === '5' || gstRate === '12'
                    ? 'bg-green-50 border-green-200'
                    : gstRate === '18'
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-orange-50 border-orange-200'
                }`}>
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      gstRate === '0' ? 'bg-gray-500' :
                      gstRate === '5' || gstRate === '12' ? 'bg-green-500' :
                      gstRate === '18' ? 'bg-blue-500' : 'bg-orange-500'
                    }`}></div>
                    <div>
                      <span className={`text-sm font-medium ${
                        gstRate === '0' ? 'text-gray-700' :
                        gstRate === '5' || gstRate === '12' ? 'text-green-700' :
                        gstRate === '18' ? 'text-blue-700' : 'text-orange-700'
                      }`}>
                        {gstRate}% GST Rate
                      </span>
                      <p className={`text-xs mt-1 ${
                        gstRate === '0' ? 'text-gray-600' :
                        gstRate === '5' || gstRate === '12' ? 'text-green-600' :
                        gstRate === '18' ? 'text-blue-600' : 'text-orange-600'
                      }`}>
                        {gstRate === '0' && 'No GST will be charged on items in this category'}
                        {gstRate === '5' && 'Low GST rate typically for essential goods and services'}
                        {gstRate === '12' && 'Standard GST rate for most goods'}
                        {gstRate === '18' && 'Standard GST rate for most services'}
                        {gstRate === '28' && 'High GST rate for luxury and sin goods'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Tax Breakdown */}
            {gstRate && parseFloat(gstRate) > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Tax Breakdown</h3>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">CGST (Central GST):</span>
                      <span className="ml-2 font-medium text-gray-900">{(parseFloat(gstRate) / 2).toFixed(1)}%</span>
                    </div>
                    <div>
                      <span className="text-gray-600">SGST (State GST):</span>
                      <span className="ml-2 font-medium text-gray-900">{(parseFloat(gstRate) / 2).toFixed(1)}%</span>
                    </div>
                    <div className="col-span-2 pt-2 border-t">
                      <span className="text-gray-600">Total GST:</span>
                      <span className="ml-2 font-medium text-gray-900">{gstRate}%</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    For inter-state transactions, IGST of {gstRate}% will be applied instead of CGST + SGST
                  </p>
                </div>
              </div>
            )}

            {/* Status */}
            <div className="space-y-4">
              <FormField error={errors.is_active}>
                <Checkbox
                  {...register('is_active')}
                  label="Active"
                  description="Make this tax category available for use"
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Category' : 'Create Category')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
