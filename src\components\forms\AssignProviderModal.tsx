'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, UserIcon, StarIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Select, Textarea } from '@/components/ui/Form';
import { Badge } from '@/components/ui/Badge';
import { useProviders } from '@/hooks/useProviders';
import { assignProviderSchema, AssignProviderFormData } from '@/lib/validations';
import { Order, Provider } from '@/types/api';

interface AssignProviderModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onAssign: (data: { provider_id: number; admin_notes?: string }) => void;
  isLoading?: boolean;
}

export default function AssignProviderModal({
  order,
  isOpen,
  onClose,
  onAssign,
  isLoading = false
}: AssignProviderModalProps) {
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  
  const { data: providersResponse } = useProviders({ 
    limit: 100,
    is_available: true,
    verification_status: 'verified'
  });

  const providers = providersResponse?.results || [];

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<AssignProviderFormData>({
    resolver: zodResolver(assignProviderSchema),
    defaultValues: {
      provider_id: 0,
      admin_notes: '',
    },
  });

  const watchedProviderId = watch('provider_id');

  // Update selected provider when form value changes
  React.useEffect(() => {
    const provider = providers.find(p => p.id === watchedProviderId);
    setSelectedProvider(provider || null);
  }, [watchedProviderId, providers]);

  const onSubmit = async (data: AssignProviderFormData) => {
    try {
      await onAssign(data);
      reset();
      onClose();
    } catch (error) {
      // Error is handled by the parent component
    }
  };

  const handleClose = () => {
    reset();
    setSelectedProvider(null);
    onClose();
  };

  if (!isOpen || !order) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Assign Provider
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Order #{order.order_number}
              </p>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Order Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Order Summary</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Customer:</span>
                  <span className="ml-2 text-gray-900">{order.customer_name}</span>
                </div>
                <div>
                  <span className="text-gray-600">Items:</span>
                  <span className="ml-2 text-gray-900">{order.items_count} items</span>
                </div>
                <div>
                  <span className="text-gray-600">Total:</span>
                  <span className="ml-2 text-gray-900">₹{order.total_amount}</span>
                </div>
                <div>
                  <span className="text-gray-600">Scheduled:</span>
                  <span className="ml-2 text-gray-900">
                    {order.scheduled_date ? new Date(order.scheduled_date).toLocaleDateString() : 'Not scheduled'}
                  </span>
                </div>
              </div>
            </div>

            {/* Provider Selection */}
            <div className="space-y-4">
              <FormField
                label="Select Provider"
                required
                error={errors.provider_id}
              >
                <Select
                  {...register('provider_id', { valueAsNumber: true })}
                  options={[
                    { value: 0, label: 'Select a provider...' },
                    ...providers.map(provider => ({
                      value: provider.id,
                      label: `${provider.user_name} - ${provider.business_name || 'Individual Provider'}`
                    }))
                  ]}
                  placeholder="Choose provider"
                  error={!!errors.provider_id}
                />
              </FormField>

              {/* Provider Details */}
              {selectedProvider && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-blue-200 rounded-full flex items-center justify-center">
                        <UserIcon className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">
                          {selectedProvider.user_name}
                        </h4>
                        <Badge variant="success">Verified</Badge>
                        {selectedProvider.is_available && (
                          <Badge variant="info">Available</Badge>
                        )}
                      </div>
                      
                      {selectedProvider.business_name && (
                        <p className="text-sm text-gray-600 mb-2">
                          Business: {selectedProvider.business_name}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <StarIcon className="w-4 h-4 text-yellow-400 mr-1" />
                          <span>{selectedProvider.rating || 'No rating'}</span>
                        </div>
                        <div>
                          <span>{selectedProvider.total_orders_completed || 0} orders completed</span>
                        </div>
                        <div>
                          <span>{selectedProvider.total_reviews || 0} reviews</span>
                        </div>
                      </div>

                      {selectedProvider.business_description && (
                        <p className="text-sm text-gray-600 mt-2">
                          {selectedProvider.business_description}
                        </p>
                      )}

                      <div className="mt-2">
                        <span className="text-sm text-gray-600">Contact: </span>
                        <span className="text-sm text-gray-900">{selectedProvider.user_mobile}</span>
                        {selectedProvider.user_email && (
                          <>
                            <span className="text-sm text-gray-600"> | </span>
                            <span className="text-sm text-gray-900">{selectedProvider.user_email}</span>
                          </>
                        )}
                      </div>

                      {selectedProvider.service_areas && selectedProvider.service_areas.length > 0 && (
                        <div className="mt-2">
                          <span className="text-sm text-gray-600">Service Areas: </span>
                          <span className="text-sm text-gray-900">
                            {selectedProvider.service_areas.join(', ')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Admin Notes */}
            <div className="space-y-4">
              <FormField
                label="Admin Notes (Optional)"
                error={errors.admin_notes}
              >
                <Textarea
                  {...register('admin_notes')}
                  placeholder="Add any notes about this assignment..."
                  rows={3}
                  error={!!errors.admin_notes}
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting || isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || isLoading || !selectedProvider}
              >
                {isSubmitting || isLoading ? 'Assigning...' : 'Assign Provider'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
