import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { schedulingApi } from '@/lib/api';
import { TimeSlot, Booking, PaginatedResponse } from '@/types/api';

// Query keys
export const schedulingKeys = {
  all: ['scheduling'] as const,
  slots: () => [...schedulingKeys.all, 'slots'] as const,
  slot: (params: Record<string, any>) => [...schedulingKeys.slots(), params] as const,
  bookings: () => [...schedulingKeys.all, 'bookings'] as const,
  booking: (params: Record<string, any>) => [...schedulingKeys.bookings(), params] as const,
  details: () => [...schedulingKeys.all, 'detail'] as const,
  detail: (id: number) => [...schedulingKeys.details(), id] as const,
};

// Get time slots with filters
export const useTimeSlots = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: schedulingKeys.slot(params),
    queryFn: () => schedulingApi.getTimeSlots(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Get bookings with filters
export const useBookings = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: schedulingKeys.booking(params),
    queryFn: () => schedulingApi.getSlotBookings(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Create time slot mutation
export const useCreateTimeSlot = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => schedulingApi.createTimeSlot(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: schedulingKeys.slots() });
      toast.success('Time slot created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create time slot');
    },
  });
};

// Update time slot mutation
export const useUpdateTimeSlot = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ slotId, data }: { slotId: number; data: any }) =>
      schedulingApi.updateTimeSlot(slotId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: schedulingKeys.slots() });
      queryClient.invalidateQueries({ queryKey: schedulingKeys.detail(variables.slotId) });
      toast.success('Time slot updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update time slot');
    },
  });
};

// Block time slot mutation
export const useBlockTimeSlot = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ slotId, data }: { slotId: number; data: { reason: string; blocked_by: string } }) =>
      schedulingApi.blockTimeSlot(slotId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: schedulingKeys.slots() });
      queryClient.invalidateQueries({ queryKey: schedulingKeys.detail(variables.slotId) });
      toast.success('Time slot blocked successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to block time slot');
    },
  });
};

// Update booking status mutation
export const useUpdateBookingStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ bookingId, status, notes }: { bookingId: number; status: string; notes?: string }) =>
      schedulingApi.updateBookingStatus(bookingId, { status, notes }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: schedulingKeys.bookings() });
      toast.success('Booking status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update booking status');
    },
  });
};

// Reschedule booking mutation
export const useRescheduleBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ bookingId, data }: { bookingId: number; data: { new_date: string; new_time_slot: string; reason?: string } }) =>
      schedulingApi.rescheduleBooking(bookingId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: schedulingKeys.bookings() });
      queryClient.invalidateQueries({ queryKey: schedulingKeys.slots() });
      toast.success('Booking rescheduled successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reschedule booking');
    },
  });
};

// Create booking mutation
export const useCreateBooking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => schedulingApi.createBooking(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: schedulingKeys.bookings() });
      queryClient.invalidateQueries({ queryKey: schedulingKeys.slots() });
      toast.success('Booking created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create booking');
    },
  });
};
