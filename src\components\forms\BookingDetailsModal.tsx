'use client';

import React from 'react';
import { XMarkIcon, CalendarIcon, UserIcon, ClockIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { formatDateTime } from '@/lib/utils';
import { Booking } from '@/types/api';

interface BookingDetailsModalProps {
  booking: Booking | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStatus?: (booking: Booking, status: string) => void;
  onReschedule?: (booking: Booking) => void;
}

export default function BookingDetailsModal({
  booking,
  isOpen,
  onClose,
  onUpdateStatus,
  onReschedule
}: BookingDetailsModalProps) {
  if (!isOpen || !booking) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'cancelled': return 'danger';
      case 'confirmed': return 'info';
      default: return 'warning';
    }
  };

  const getStatusActions = () => {
    const actions = [];

    if (booking.booking_status === 'pending') {
      actions.push(
        <Button
          key="confirm"
          size="sm"
          onClick={() => onUpdateStatus?.(booking, 'confirmed')}
        >
          <CheckCircleIcon className="w-4 h-4 mr-2" />
          Confirm Booking
        </Button>
      );
    }

    if (booking.booking_status === 'confirmed') {
      actions.push(
        <Button
          key="complete"
          size="sm"
          onClick={() => onUpdateStatus?.(booking, 'completed')}
        >
          <CheckCircleIcon className="w-4 h-4 mr-2" />
          Mark Complete
        </Button>
      );
    }

    if (booking.booking_status !== 'completed' && booking.booking_status !== 'cancelled') {
      actions.push(
        <Button
          key="cancel"
          variant="danger"
          size="sm"
          onClick={() => onUpdateStatus?.(booking, 'cancelled')}
        >
          <XCircleIcon className="w-4 h-4 mr-2" />
          Cancel Booking
        </Button>
      );

      actions.push(
        <Button
          key="reschedule"
          variant="outline"
          size="sm"
          onClick={() => onReschedule?.(booking)}
        >
          <ClockIcon className="w-4 h-4 mr-2" />
          Reschedule
        </Button>
      );
    }

    return actions;
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <CalendarIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Booking Details
                </h2>
                <p className="text-sm text-gray-500">
                  Order #{booking.order_id}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* Status and Actions */}
            <div className="flex items-center justify-between">
              <Badge variant={getStatusColor(booking.booking_status)}>
                {booking.booking_status.toUpperCase()}
              </Badge>
              <div className="flex items-center space-x-2">
                {getStatusActions()}
              </div>
            </div>

            {/* Customer Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <UserIcon className="w-5 h-5 mr-2" />
                  Customer Information
                </h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Name:</span>
                    <span className="ml-2 text-sm text-gray-900">{booking.customer_name}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Mobile:</span>
                    <span className="ml-2 text-sm text-gray-900">{booking.customer_mobile}</span>
                  </div>
                  {booking.customer_notes && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Notes:</span>
                      <p className="mt-1 text-sm text-gray-900">{booking.customer_notes}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <ClockIcon className="w-5 h-5 mr-2" />
                  Schedule Information
                </h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Date:</span>
                    <span className="ml-2 text-sm text-gray-900">
                      {new Date(booking.scheduled_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Time:</span>
                    <span className="ml-2 text-sm text-gray-900">{booking.scheduled_time}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Quantity:</span>
                    <span className="ml-2 text-sm text-gray-900">{booking.quantity}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Provider Information */}
            {booking.provider_name && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Assigned Provider</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Provider:</span>
                    <span className="ml-2 text-sm text-gray-900">{booking.provider_name}</span>
                  </div>
                  {booking.provider_notes && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Provider Notes:</span>
                      <p className="mt-1 text-sm text-gray-900">{booking.provider_notes}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Services */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Services</h3>
              <div className="space-y-2">
                {booking.service_names.map((service, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-900">{service}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Booking Timeline</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Booked:</span>
                    <span className="ml-2 text-sm text-gray-900">{formatDateTime(booking.booked_at)}</span>
                  </div>
                </div>

                {booking.confirmed_at && (
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Confirmed:</span>
                      <span className="ml-2 text-sm text-gray-900">{formatDateTime(booking.confirmed_at)}</span>
                    </div>
                  </div>
                )}

                {booking.completed_at && (
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-600 rounded-full mr-3"></div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Completed:</span>
                      <span className="ml-2 text-sm text-gray-900">{formatDateTime(booking.completed_at)}</span>
                    </div>
                  </div>
                )}

                {booking.cancelled_at && (
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Cancelled:</span>
                      <span className="ml-2 text-sm text-gray-900">{formatDateTime(booking.cancelled_at)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
