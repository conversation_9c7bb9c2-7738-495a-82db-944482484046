import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { settingsApi } from '@/lib/api';

// Query keys
export const settingsKeys = {
  all: ['settings'] as const,
  general: () => [...settingsKeys.all, 'general'] as const,
  business: () => [...settingsKeys.all, 'business'] as const,
  notifications: () => [...settingsKeys.all, 'notifications'] as const,
  security: () => [...settingsKeys.all, 'security'] as const,
  integrations: () => [...settingsKeys.all, 'integrations'] as const,
};

// Get general settings
export const useGeneralSettings = () => {
  return useQuery({
    queryKey: settingsKeys.general(),
    queryFn: () => settingsApi.getGeneralSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get business settings
export const useBusinessSettings = () => {
  return useQuery({
    queryKey: settingsKeys.business(),
    queryFn: () => settingsApi.getBusinessSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get notification settings
export const useNotificationSettings = () => {
  return useQuery({
    queryKey: settingsKeys.notifications(),
    queryFn: () => settingsApi.getNotificationSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get security settings
export const useSecuritySettings = () => {
  return useQuery({
    queryKey: settingsKeys.security(),
    queryFn: () => settingsApi.getSecuritySettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get integration settings
export const useIntegrationSettings = () => {
  return useQuery({
    queryKey: settingsKeys.integrations(),
    queryFn: () => settingsApi.getIntegrationSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Update general settings mutation
export const useUpdateGeneralSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => settingsApi.updateGeneralSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.general() });
      toast.success('General settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update general settings');
    },
  });
};

// Update business settings mutation
export const useUpdateBusinessSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => settingsApi.updateBusinessSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.business() });
      toast.success('Business settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update business settings');
    },
  });
};

// Update notification settings mutation
export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => settingsApi.updateNotificationSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.notifications() });
      toast.success('Notification settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update notification settings');
    },
  });
};

// Update security settings mutation
export const useUpdateSecuritySettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => settingsApi.updateSecuritySettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.security() });
      toast.success('Security settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update security settings');
    },
  });
};

// Update integration settings mutation
export const useUpdateIntegrationSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => settingsApi.updateIntegrationSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.integrations() });
      toast.success('Integration settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update integration settings');
    },
  });
};

// Test integration connection mutation
export const useTestIntegration = () => {
  return useMutation({
    mutationFn: ({ integration, config }: { integration: string; config: any }) =>
      settingsApi.testIntegration(integration, config),
    onSuccess: (data) => {
      if (data.success) {
        toast.success('Integration test successful');
      } else {
        toast.error(data.message || 'Integration test failed');
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to test integration');
    },
  });
};
