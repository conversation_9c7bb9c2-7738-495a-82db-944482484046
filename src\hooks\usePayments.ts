import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { paymentsApi } from '@/lib/api';
import { PaymentTransaction, PaginatedResponse } from '@/types/api';

// Query keys
export const paymentKeys = {
  all: ['payments'] as const,
  lists: () => [...paymentKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...paymentKeys.lists(), params] as const,
  details: () => [...paymentKeys.all, 'detail'] as const,
  detail: (transactionId: string) => [...paymentKeys.details(), transactionId] as const,
  config: () => [...paymentKeys.all, 'config'] as const,
};

// Get payment transactions list with filters
export const usePaymentTransactions = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: paymentKeys.list(params),
    queryFn: () => paymentsApi.getTransactions(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Get single transaction details
export const usePaymentTransaction = (transactionId: string) => {
  return useQuery({
    queryKey: paymentKeys.detail(transactionId),
    queryFn: () => paymentsApi.getTransactionDetail(transactionId),
    enabled: !!transactionId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Get payment configuration
export const usePaymentConfig = () => {
  return useQuery({
    queryKey: paymentKeys.config(),
    queryFn: () => paymentsApi.getPaymentConfig(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Update payment configuration mutation
export const useUpdatePaymentConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => paymentsApi.updatePaymentConfig(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: paymentKeys.config() });
      toast.success('Payment configuration updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update payment configuration');
    },
  });
};

// Process refund mutation
export const useProcessRefund = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ transactionId, data }: { transactionId: string; data: { amount?: string; reason: string } }) =>
      paymentsApi.processRefund(transactionId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.detail(variables.transactionId) });
      toast.success('Refund processed successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to process refund');
    },
  });
};

// Retry payment mutation
export const useRetryPayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (transactionId: string) => paymentsApi.retryPayment(transactionId),
    onSuccess: (_, transactionId) => {
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.detail(transactionId) });
      toast.success('Payment retry initiated');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to retry payment');
    },
  });
};
