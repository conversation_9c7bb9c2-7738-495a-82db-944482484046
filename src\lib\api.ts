import axios, { AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';
import { AuthTokens, LoginResponse, User, ApiError } from '@/types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

// Create axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
});

// Token management
export const getTokens = (): AuthTokens => {
  if (typeof window === 'undefined') return { access: '', refresh: '' };

  const access = localStorage.getItem('access_token') || '';
  const refresh = localStorage.getItem('refresh_token') || '';
  return { access, refresh };
};

export const setTokens = (tokens: AuthTokens): void => {
  if (typeof window === 'undefined') return;

  localStorage.setItem('access_token', tokens.access);
  localStorage.setItem('refresh_token', tokens.refresh);
};

export const clearTokens = (): void => {
  if (typeof window === 'undefined') return;

  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const { access } = getTokens();
    if (access) {
      config.headers.Authorization = `Bearer ${access}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const { refresh } = getTokens();
      if (refresh) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh,
          });

          const newTokens = {
            access: response.data.access,
            refresh: refresh,
          };

          setTokens(newTokens);
          originalRequest.headers.Authorization = `Bearer ${response.data.access}`;

          return apiClient(originalRequest);
        } catch (refreshError) {
          clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
          return Promise.reject(refreshError);
        }
      } else {
        clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.status === 403) {
      toast.error('Access denied. You do not have permission to perform this action.');
    } else if (error.response?.status === 404) {
      toast.error('Resource not found.');
    }

    return Promise.reject(error);
  }
);

// Helper function to handle API responses
const handleApiResponse = <T>(response: AxiosResponse<T>): T => {
  return response.data;
};

// Helper function to handle API errors
const handleApiError = (error: AxiosError): never => {
  const apiError: ApiError = {
    message: (error.response?.data as any)?.message ||
             (error.response?.data as any)?.error ||
             error.message ||
             'An error occurred',
    details: (error.response?.data as any)?.details || {},
    status: error.response?.status || 0,
  };
  throw apiError;
};

// Authentication API
export const authApi = {
  // Staff login with email/password
  loginStaff: async (credentials: { email: string; password: string }): Promise<LoginResponse> => {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/login/email/', credentials);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get user profile
  getProfile: async (): Promise<User> => {
    try {
      const response = await apiClient.get<User>('/auth/profile/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update user profile
  updateProfile: async (data: Partial<User>): Promise<User> => {
    try {
      const response = await apiClient.put<User>('/auth/profile/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Change password
  changePassword: async (data: { old_password: string; new_password: string; confirm_password: string }): Promise<void> => {
    try {
      const response = await apiClient.post('/auth/change-password/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      const response = await apiClient.post('/auth/logout/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Orders API
export const ordersApi = {
  // Get all orders (staff can see all)
  getOrders: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/orders/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get order details
  getOrderDetail: async (orderNumber: string) => {
    try {
      const response = await apiClient.get(`/orders/${orderNumber}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update order status
  updateOrderStatus: async (orderNumber: string, data: { status: string; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/orders/${orderNumber}/status/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Assign provider to order
  assignProvider: async (orderNumber: string, data: { provider_id: number; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/orders/${orderNumber}/assign-provider/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Cancel order
  cancelOrder: async (orderNumber: string, data: { reason: string; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/orders/${orderNumber}/cancel/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create order (admin)
  createOrder: async (data: any) => {
    try {
      const response = await apiClient.post('/orders/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Users API
export const usersApi = {
  // Get all users
  getUsers: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/auth/users/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get user details
  getUserDetail: async (userId: number) => {
    try {
      const response = await apiClient.get(`/auth/users/${userId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create user
  createUser: async (data: FormData | any) => {
    try {
      const config = data instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};
      const response = await apiClient.post('/auth/users/', data, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update user
  updateUser: async (userId: number, data: FormData | Partial<User>) => {
    try {
      const config = data instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};
      const response = await apiClient.put(`/auth/users/${userId}/`, data, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Lock/unlock user
  toggleUserLock: async (userId: number, data: { is_locked: boolean; lockout_duration?: number }) => {
    try {
      const response = await apiClient.post(`/auth/users/${userId}/toggle-lock/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Bulk user operations
  bulkUpdateUsers: async (userIds: number[], data: Partial<User>) => {
    try {
      const response = await apiClient.post('/auth/users/bulk-update/', {
        user_ids: userIds,
        ...data
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Unlock user account
  unlockUser: async (userId: number) => {
    try {
      const response = await apiClient.post(`/auth/users/${userId}/unlock/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Providers API
export const providersApi = {
  // Get all providers
  getProviders: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/providers/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get provider details
  getProviderDetail: async (providerId: number) => {
    try {
      const response = await apiClient.get(`/providers/${providerId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update provider verification status
  updateProviderVerification: async (providerId: number, data: { verification_status: string; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/providers/${providerId}/verification/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Toggle provider availability
  toggleProviderAvailability: async (providerId: number, data: { is_available: boolean }) => {
    try {
      const response = await apiClient.post(`/providers/${providerId}/availability/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create provider
  createProvider: async (data: FormData) => {
    try {
      const response = await apiClient.post('/providers/', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update provider
  updateProvider: async (providerId: number, data: FormData) => {
    try {
      const response = await apiClient.patch(`/providers/${providerId}/`, data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Services API
export const servicesApi = {
  // Get all services
  getServices: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/catalogue/services/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get service details
  getServiceDetail: async (serviceId: number) => {
    try {
      const response = await apiClient.get(`/catalogue/services/${serviceId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create service
  createService: async (data: FormData | any) => {
    try {
      const config = data instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};
      const response = await apiClient.post('/catalogue/services/', data, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update service
  updateService: async (serviceId: number, data: FormData | any) => {
    try {
      const config = data instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};
      const response = await apiClient.put(`/catalogue/services/${serviceId}/`, data, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete service
  deleteService: async (serviceId: number) => {
    try {
      const response = await apiClient.delete(`/catalogue/services/${serviceId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Bulk operations
  bulkUpdateServices: async (serviceIds: number[], data: any) => {
    try {
      const response = await apiClient.post('/catalogue/services/bulk-update/', {
        service_ids: serviceIds,
        ...data
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Bulk delete services
  bulkDeleteServices: async (serviceIds: number[]) => {
    try {
      const response = await apiClient.post('/catalogue/services/bulk-delete/', {
        service_ids: serviceIds
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Toggle service status
  toggleServiceStatus: async (serviceId: number, isActive: boolean) => {
    try {
      const response = await apiClient.post(`/catalogue/services/${serviceId}/toggle-status/`, {
        is_active: isActive
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Duplicate service
  duplicateService: async (serviceId: number) => {
    try {
      const response = await apiClient.post(`/catalogue/services/${serviceId}/duplicate/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Import services
  importServices: async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await apiClient.post('/catalogue/services/import/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Export services
  exportServices: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/catalogue/services/export/', {
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Categories API
export const categoriesApi = {
  // Get all categories
  getCategories: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/catalogue/categories/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get category tree (hierarchical)
  getCategoryTree: async () => {
    try {
      const response = await apiClient.get('/catalogue/categories/tree/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get category details
  getCategoryDetail: async (categoryId: number) => {
    try {
      const response = await apiClient.get(`/catalogue/categories/${categoryId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create category
  createCategory: async (data: FormData | any) => {
    try {
      const config = data instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};
      const response = await apiClient.post('/catalogue/categories/', data, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update category
  updateCategory: async (categoryId: number, data: FormData | any) => {
    try {
      const config = data instanceof FormData ? {
        headers: { 'Content-Type': 'multipart/form-data' }
      } : {};
      const response = await apiClient.put(`/catalogue/categories/${categoryId}/`, data, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete category
  deleteCategory: async (categoryId: number) => {
    try {
      const response = await apiClient.delete(`/catalogue/categories/${categoryId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Reorder categories
  reorderCategories: async (categoryOrders: Array<{ id: number; parent?: number; order: number }>) => {
    try {
      const response = await apiClient.post('/catalogue/categories/reorder/', {
        category_orders: categoryOrders
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Toggle category status
  toggleCategoryStatus: async (categoryId: number, isActive: boolean) => {
    try {
      const response = await apiClient.post(`/catalogue/categories/${categoryId}/toggle-status/`, {
        is_active: isActive
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Payments API
export const paymentsApi = {
  // Get all transactions
  getTransactions: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/payments/transactions/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get transaction details
  getTransactionDetail: async (transactionId: string) => {
    try {
      const response = await apiClient.get(`/payments/transactions/${transactionId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Process refund
  processRefund: async (transactionId: string, data: { amount?: string; reason: string }) => {
    try {
      const response = await apiClient.post(`/payments/transactions/${transactionId}/refund/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get payment configuration
  getPaymentConfig: async () => {
    try {
      const response = await apiClient.get('/payments/config/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update payment configuration
  updatePaymentConfig: async (data: any) => {
    try {
      const response = await apiClient.put('/payments/config/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Retry payment
  retryPayment: async (transactionId: string) => {
    try {
      const response = await apiClient.post(`/payments/transactions/${transactionId}/retry/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Analytics API
export const analyticsApi = {
  // Get dashboard stats (using orders dashboard endpoint)
  getDashboardStats: async () => {
    try {
      const response = await apiClient.get('/orders/dashboard/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get user statistics (using auth admin endpoint)
  getUserStats: async () => {
    try {
      const response = await apiClient.get('/auth/admin/user-stats/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Scheduling API
export const schedulingApi = {
  // Get time slots
  getTimeSlots: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/scheduling/slots/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create time slot
  createTimeSlot: async (data: any) => {
    try {
      const response = await apiClient.post('/scheduling/slots/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update time slot
  updateTimeSlot: async (slotId: number, data: any) => {
    try {
      const response = await apiClient.put(`/scheduling/slots/${slotId}/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Block time slot
  blockTimeSlot: async (slotId: number, data: { reason: string; blocked_by: string }) => {
    try {
      const response = await apiClient.post(`/scheduling/slots/${slotId}/block/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get slot bookings
  getSlotBookings: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/scheduling/bookings/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update booking status
  updateBookingStatus: async (bookingId: number, data: { status: string; notes?: string }) => {
    try {
      const response = await apiClient.patch(`/scheduling/bookings/${bookingId}/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Reschedule booking
  rescheduleBooking: async (bookingId: number, data: { new_date: string; new_time_slot: string; reason?: string }) => {
    try {
      const response = await apiClient.post(`/scheduling/bookings/${bookingId}/reschedule/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create booking
  createBooking: async (data: any) => {
    try {
      const response = await apiClient.post('/scheduling/bookings/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Coupons API
export const couponsApi = {
  // Get all coupons
  getCoupons: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/coupons/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get coupon details
  getCouponDetail: async (couponId: number) => {
    try {
      const response = await apiClient.get(`/coupons/${couponId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create coupon
  createCoupon: async (data: any) => {
    try {
      const response = await apiClient.post('/coupons/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update coupon
  updateCoupon: async (couponId: number, data: any) => {
    try {
      const response = await apiClient.put(`/coupons/${couponId}/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete coupon
  deleteCoupon: async (couponId: number) => {
    try {
      const response = await apiClient.delete(`/coupons/${couponId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Toggle coupon status
  toggleCouponStatus: async (couponId: number, data: { is_active: boolean }) => {
    try {
      const response = await apiClient.post(`/coupons/${couponId}/toggle/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Taxes API
export const taxesApi = {
  // Get tax categories
  getTaxCategories: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/taxes/categories/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create tax category
  createTaxCategory: async (data: any) => {
    try {
      const response = await apiClient.post('/taxes/categories/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update tax category
  updateTaxCategory: async (categoryId: number, data: any) => {
    try {
      const response = await apiClient.put(`/taxes/categories/${categoryId}/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete tax category
  deleteTaxCategory: async (categoryId: number) => {
    try {
      const response = await apiClient.delete(`/taxes/categories/${categoryId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Toggle tax category status
  toggleTaxCategoryStatus: async (categoryId: number, data: { is_active: boolean }) => {
    try {
      const response = await apiClient.post(`/taxes/categories/${categoryId}/toggle/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get GST rates
  getGSTRates: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/taxes/gst-rates/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create GST rate
  createGSTRate: async (data: any) => {
    try {
      const response = await apiClient.post('/taxes/gst-rates/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update GST rate
  updateGSTRate: async (rateId: number, data: any) => {
    try {
      const response = await apiClient.put(`/taxes/gst-rates/${rateId}/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete GST rate
  deleteGSTRate: async (rateId: number) => {
    try {
      const response = await apiClient.delete(`/taxes/gst-rates/${rateId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Toggle GST rate status
  toggleGSTRateStatus: async (rateId: number, data: { is_active: boolean }) => {
    try {
      const response = await apiClient.post(`/taxes/gst-rates/${rateId}/toggle/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get tax reports
  getTaxReports: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/taxes/reports/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Settings API
export const settingsApi = {
  // Get general settings
  getGeneralSettings: async () => {
    try {
      const response = await apiClient.get('/settings/general/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update general settings
  updateGeneralSettings: async (data: any) => {
    try {
      const response = await apiClient.put('/settings/general/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get business settings
  getBusinessSettings: async () => {
    try {
      const response = await apiClient.get('/settings/business/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update business settings
  updateBusinessSettings: async (data: any) => {
    try {
      const response = await apiClient.put('/settings/business/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get notification settings
  getNotificationSettings: async () => {
    try {
      const response = await apiClient.get('/settings/notifications/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update notification settings
  updateNotificationSettings: async (data: any) => {
    try {
      const response = await apiClient.put('/settings/notifications/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get security settings
  getSecuritySettings: async () => {
    try {
      const response = await apiClient.get('/settings/security/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update security settings
  updateSecuritySettings: async (data: any) => {
    try {
      const response = await apiClient.put('/settings/security/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get integration settings
  getIntegrationSettings: async () => {
    try {
      const response = await apiClient.get('/settings/integrations/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update integration settings
  updateIntegrationSettings: async (data: any) => {
    try {
      const response = await apiClient.put('/settings/integrations/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Test integration
  testIntegration: async (integration: string, config: any) => {
    try {
      const response = await apiClient.post(`/settings/integrations/${integration}/test/`, config);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};
