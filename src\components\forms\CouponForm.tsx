'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, TicketIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Select, Checkbox } from '@/components/ui/Form';
import { useCreateCoupon, useUpdateCoupon } from '@/hooks/useCoupons';
import { couponSchema, CouponFormData } from '@/lib/validations';
import { Coupon } from '@/types/api';

interface CouponFormProps {
  coupon?: Coupon;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function CouponForm({ coupon, isOpen, onClose, onSuccess }: CouponFormProps) {
  const isEditing = !!coupon;
  const createCouponMutation = useCreateCoupon();
  const updateCouponMutation = useUpdateCoupon();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      code: '',
      name: '',
      discount_type: 'percentage',
      discount_value: '',
      minimum_order_amount: '',
      maximum_discount_amount: '',
      usage_limit: undefined,
      usage_limit_per_user: undefined,
      valid_from: '',
      valid_until: '',
      is_active: true,
    },
  });

  const discountType = watch('discount_type');

  // Reset form when coupon changes
  useEffect(() => {
    if (coupon) {
      reset({
        code: coupon.code,
        name: coupon.name,
        discount_type: coupon.discount_type,
        discount_value: coupon.discount_value.toString(),
        minimum_order_amount: coupon.minimum_order_amount?.toString() || '',
        maximum_discount_amount: coupon.maximum_discount_amount?.toString() || '',
        usage_limit: coupon.usage_limit || undefined,
        usage_limit_per_user: coupon.usage_limit_per_user || undefined,
        valid_from: coupon.valid_from.split('T')[0], // Convert to date format
        valid_until: coupon.valid_until.split('T')[0], // Convert to date format
        is_active: coupon.is_active,
      });
    } else {
      reset();
    }
  }, [coupon, reset]);

  const onSubmit = async (data: CouponFormData) => {
    try {
      // Convert dates to ISO format
      const formattedData = {
        ...data,
        valid_from: new Date(data.valid_from).toISOString(),
        valid_until: new Date(data.valid_until).toISOString(),
        // Remove empty optional fields
        minimum_order_amount: data.minimum_order_amount || undefined,
        maximum_discount_amount: data.maximum_discount_amount || undefined,
        usage_limit: data.usage_limit || undefined,
        usage_limit_per_user: data.usage_limit_per_user || undefined,
      };

      if (isEditing && coupon) {
        await updateCouponMutation.mutateAsync({
          couponId: coupon.id,
          data: formattedData,
        });
      } else {
        await createCouponMutation.mutateAsync(formattedData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <TicketIcon className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit Coupon' : 'Create New Coupon'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Coupon Code"
                  required
                  error={errors.code}
                >
                  <Input
                    {...register('code')}
                    placeholder="e.g., SAVE20"
                    className="uppercase"
                    error={!!errors.code}
                  />
                </FormField>

                <FormField
                  label="Coupon Name"
                  required
                  error={errors.name}
                >
                  <Input
                    {...register('name')}
                    placeholder="e.g., 20% Off All Services"
                    error={!!errors.name}
                  />
                </FormField>
              </div>
            </div>

            {/* Discount Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Discount Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Discount Type"
                  required
                  error={errors.discount_type}
                >
                  <Select
                    {...register('discount_type')}
                    options={[
                      { value: 'percentage', label: 'Percentage' },
                      { value: 'fixed', label: 'Fixed Amount' }
                    ]}
                    error={!!errors.discount_type}
                  />
                </FormField>

                <FormField
                  label={`Discount Value ${discountType === 'percentage' ? '(%)' : '(₹)'}`}
                  required
                  error={errors.discount_value}
                >
                  <Input
                    {...register('discount_value')}
                    type="number"
                    step="0.01"
                    placeholder={discountType === 'percentage' ? '20' : '100'}
                    error={!!errors.discount_value}
                  />
                </FormField>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Minimum Order Amount (₹)"
                  error={errors.minimum_order_amount}
                >
                  <Input
                    {...register('minimum_order_amount')}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    error={!!errors.minimum_order_amount}
                  />
                </FormField>

                {discountType === 'percentage' && (
                  <FormField
                    label="Maximum Discount Amount (₹)"
                    error={errors.maximum_discount_amount}
                  >
                    <Input
                      {...register('maximum_discount_amount')}
                      type="number"
                      step="0.01"
                      placeholder="500.00"
                      error={!!errors.maximum_discount_amount}
                    />
                  </FormField>
                )}
              </div>
            </div>

            {/* Usage Limits */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Usage Limits</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Total Usage Limit"
                  error={errors.usage_limit}
                >
                  <Input
                    {...register('usage_limit', { valueAsNumber: true })}
                    type="number"
                    min="1"
                    placeholder="Leave empty for unlimited"
                    error={!!errors.usage_limit}
                  />
                </FormField>

                <FormField
                  label="Usage Limit Per User"
                  error={errors.usage_limit_per_user}
                >
                  <Input
                    {...register('usage_limit_per_user', { valueAsNumber: true })}
                    type="number"
                    min="1"
                    placeholder="Leave empty for unlimited"
                    error={!!errors.usage_limit_per_user}
                  />
                </FormField>
              </div>
            </div>

            {/* Validity Period */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Validity Period</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Valid From"
                  required
                  error={errors.valid_from}
                >
                  <Input
                    {...register('valid_from')}
                    type="date"
                    min={new Date().toISOString().split('T')[0]}
                    error={!!errors.valid_from}
                  />
                </FormField>

                <FormField
                  label="Valid Until"
                  required
                  error={errors.valid_until}
                >
                  <Input
                    {...register('valid_until')}
                    type="date"
                    min={new Date().toISOString().split('T')[0]}
                    error={!!errors.valid_until}
                  />
                </FormField>
              </div>
            </div>

            {/* Status */}
            <div className="space-y-4">
              <FormField error={errors.is_active}>
                <Checkbox
                  {...register('is_active')}
                  label="Active"
                  description="Make this coupon available for use"
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Coupon' : 'Create Coupon')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
