'use client';

import React from 'react';
import { XMarkIcon, UserIcon, MapPinIcon, CalendarIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { Order } from '@/types/api';

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onEditOrder?: (order: Order) => void;
  onAssignProvider?: (order: Order) => void;
  onUpdateStatus?: (order: Order, status: string) => void;
}

export default function OrderDetailsModal({
  order,
  isOpen,
  onClose,
  onEditOrder,
  onAssignProvider,
  onUpdateStatus
}: OrderDetailsModalProps) {
  if (!isOpen || !order) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'cancelled': return 'danger';
      case 'in_progress': return 'info';
      case 'confirmed': return 'warning';
      case 'assigned': return 'info';
      default: return 'secondary';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'failed': return 'danger';
      case 'refunded': return 'warning';
      default: return 'secondary';
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Order #{order.order_number}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Created {formatDateTime(order.created_at)}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* Order Status and Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Badge variant={getStatusColor(order.status)}>
                  {order.status.replace('_', ' ').toUpperCase()}
                </Badge>
                <Badge variant={getPaymentStatusColor(order.payment_status)}>
                  Payment: {order.payment_status.toUpperCase()}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                {onEditOrder && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditOrder(order)}
                  >
                    Edit Order
                  </Button>
                )}
                {onAssignProvider && !order.assigned_provider && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onAssignProvider(order)}
                  >
                    <UserIcon className="w-4 h-4 mr-2" />
                    Assign Provider
                  </Button>
                )}
                {onUpdateStatus && order.status !== 'completed' && order.status !== 'cancelled' && (
                  <div className="flex space-x-1">
                    {order.status === 'pending' && (
                      <Button
                        size="sm"
                        onClick={() => onUpdateStatus(order, 'confirmed')}
                      >
                        Confirm
                      </Button>
                    )}
                    {(order.status === 'confirmed' || order.status === 'assigned') && (
                      <Button
                        size="sm"
                        onClick={() => onUpdateStatus(order, 'in_progress')}
                      >
                        Start
                      </Button>
                    )}
                    {order.status === 'in_progress' && (
                      <Button
                        size="sm"
                        onClick={() => onUpdateStatus(order, 'completed')}
                      >
                        Complete
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <UserIcon className="w-5 h-5 mr-2" />
                  Customer Information
                </h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Name:</span>
                    <span className="ml-2 text-sm text-gray-900">{order.customer_name}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Mobile:</span>
                    <span className="ml-2 text-sm text-gray-900">{order.customer_mobile}</span>
                  </div>
                  {order.customer_notes && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Notes:</span>
                      <p className="mt-1 text-sm text-gray-900">{order.customer_notes}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <MapPinIcon className="w-5 h-5 mr-2" />
                  Delivery Address
                </h3>
                <div className="text-sm text-gray-900">
                  {order.delivery_address ? (
                    <div>
                      <p>{order.delivery_address.street}</p>
                      <p>{order.delivery_address.city}, {order.delivery_address.state}</p>
                      <p>{order.delivery_address.zip_code}</p>
                      {order.delivery_address.landmark && (
                        <p className="text-gray-600">Landmark: {order.delivery_address.landmark}</p>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">No address provided</p>
                  )}
                </div>
              </div>
            </div>

            {/* Provider Information */}
            {order.assigned_provider && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Assigned Provider
                </h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Name:</span>
                    <span className="ml-2 text-sm text-gray-900">{order.assigned_provider_name}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Mobile:</span>
                    <span className="ml-2 text-sm text-gray-900">{order.assigned_provider_mobile}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Scheduling Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                <CalendarIcon className="w-5 h-5 mr-2" />
                Scheduling
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium text-gray-700">Date:</span>
                  <span className="ml-2 text-sm text-gray-900">
                    {order.scheduled_date ? formatDateTime(order.scheduled_date) : 'Not scheduled'}
                  </span>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-700">Time Slot:</span>
                  <span className="ml-2 text-sm text-gray-900">
                    {order.scheduled_time_slot || 'Not scheduled'}
                  </span>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Order Items</h3>
              <div className="bg-white border rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {order.items.map((item, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {item.service_title}
                            </div>
                            {item.special_instructions && (
                              <div className="text-sm text-gray-500">
                                {item.special_instructions}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.unit_price)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.total_price)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Payment Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                <CreditCardIcon className="w-5 h-5 mr-2" />
                Payment Summary
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Subtotal:</span>
                  <span className="text-sm text-gray-900">{formatCurrency(order.subtotal)}</span>
                </div>
                {order.discount_amount !== '0.00' && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Discount:</span>
                    <span className="text-sm text-green-600">-{formatCurrency(order.discount_amount)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Tax:</span>
                  <span className="text-sm text-gray-900">{formatCurrency(order.tax_amount)}</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-base font-medium text-gray-900">Total:</span>
                  <span className="text-base font-medium text-gray-900">{formatCurrency(order.total_amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Payment Method:</span>
                  <span className="text-sm text-gray-900">{order.payment_method?.toUpperCase()}</span>
                </div>
              </div>
            </div>

            {/* Admin Notes */}
            {order.admin_notes && (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Admin Notes</h3>
                <p className="text-sm text-gray-700">{order.admin_notes}</p>
              </div>
            )}
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
