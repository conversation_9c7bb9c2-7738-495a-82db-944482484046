'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, CalculatorIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Checkbox } from '@/components/ui/Form';
import { useCreateGSTRate, useUpdateGSTRate } from '@/hooks/useTaxes';
import { gstRateSchema, GSTRateFormData } from '@/lib/validations';
import { GSTRate } from '@/types/api';

interface GSTRateFormProps {
  gstRate?: GSTRate;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function GSTRateForm({ gstRate, isOpen, onClose, onSuccess }: GSTRateFormProps) {
  const isEditing = !!gstRate;
  const createGSTRateMutation = useCreateGSTRate();
  const updateGSTRateMutation = useUpdateGSTRate();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
  } = useForm<GSTRateFormData>({
    resolver: zodResolver(gstRateSchema),
    defaultValues: {
      rate: '',
      description: '',
      is_active: true,
    },
  });

  const rate = watch('rate');

  // Reset form when gstRate changes
  useEffect(() => {
    if (gstRate) {
      reset({
        rate: gstRate.rate.toString(),
        description: gstRate.description,
        is_active: gstRate.is_active,
      });
    } else {
      reset();
    }
  }, [gstRate, reset]);

  const onSubmit = async (data: GSTRateFormData) => {
    try {
      if (isEditing && gstRate) {
        await updateGSTRateMutation.mutateAsync({
          rateId: gstRate.id,
          data,
        });
      } else {
        await createGSTRateMutation.mutateAsync(data);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                <CalculatorIcon className="w-5 h-5 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit GST Rate' : 'Create New GST Rate'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Rate Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">GST Rate Configuration</h3>
              
              <FormField
                label="GST Rate (%)"
                required
                error={errors.rate}
              >
                <Input
                  {...register('rate')}
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  placeholder="18.00"
                  error={!!errors.rate}
                />
              </FormField>

              <FormField
                label="Description"
                required
                error={errors.description}
              >
                <Textarea
                  {...register('description')}
                  placeholder="Describe what this GST rate applies to (e.g., Standard rate for services)"
                  rows={3}
                  error={!!errors.description}
                />
              </FormField>
            </div>

            {/* Rate Information */}
            {rate && parseFloat(rate) >= 0 && (
              <div className={`p-4 rounded-lg border ${
                parseFloat(rate) === 0 
                  ? 'bg-gray-50 border-gray-200' 
                  : parseFloat(rate) <= 5
                  ? 'bg-green-50 border-green-200'
                  : parseFloat(rate) <= 12
                  ? 'bg-blue-50 border-blue-200'
                  : parseFloat(rate) <= 18
                  ? 'bg-yellow-50 border-yellow-200'
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center mb-3">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    parseFloat(rate) === 0 ? 'bg-gray-500' :
                    parseFloat(rate) <= 5 ? 'bg-green-500' :
                    parseFloat(rate) <= 12 ? 'bg-blue-500' :
                    parseFloat(rate) <= 18 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className={`text-sm font-medium ${
                    parseFloat(rate) === 0 ? 'text-gray-700' :
                    parseFloat(rate) <= 5 ? 'text-green-700' :
                    parseFloat(rate) <= 12 ? 'text-blue-700' :
                    parseFloat(rate) <= 18 ? 'text-yellow-700' : 'text-red-700'
                  }`}>
                    {rate}% GST Rate
                  </span>
                </div>

                {parseFloat(rate) > 0 && (
                  <div className="bg-white p-3 rounded border">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Tax Breakdown</h4>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-600">CGST:</span>
                        <span className="ml-2 font-medium">{(parseFloat(rate) / 2).toFixed(2)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">SGST:</span>
                        <span className="ml-2 font-medium">{(parseFloat(rate) / 2).toFixed(2)}%</span>
                      </div>
                      <div className="col-span-2 pt-2 border-t">
                        <span className="text-gray-600">IGST (Inter-state):</span>
                        <span className="ml-2 font-medium">{rate}%</span>
                      </div>
                    </div>
                  </div>
                )}

                <p className={`text-xs mt-2 ${
                  parseFloat(rate) === 0 ? 'text-gray-600' :
                  parseFloat(rate) <= 5 ? 'text-green-600' :
                  parseFloat(rate) <= 12 ? 'text-blue-600' :
                  parseFloat(rate) <= 18 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {parseFloat(rate) === 0 && 'Exempt from GST - No tax will be charged'}
                  {parseFloat(rate) > 0 && parseFloat(rate) <= 5 && 'Low GST rate - Typically for essential goods'}
                  {parseFloat(rate) > 5 && parseFloat(rate) <= 12 && 'Standard GST rate - Common for most goods'}
                  {parseFloat(rate) > 12 && parseFloat(rate) <= 18 && 'Standard GST rate - Common for services'}
                  {parseFloat(rate) > 18 && 'High GST rate - Luxury items and sin goods'}
                </p>
              </div>
            )}

            {/* Examples */}
            {rate && parseFloat(rate) >= 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Example Calculation</h4>
                <div className="text-sm text-blue-800">
                  <p>For a service priced at ₹1,000:</p>
                  <div className="mt-2 space-y-1">
                    <div>Base Amount: ₹1,000.00</div>
                    {parseFloat(rate) > 0 ? (
                      <>
                        <div>GST ({rate}%): ₹{(1000 * parseFloat(rate) / 100).toFixed(2)}</div>
                        <div className="font-medium border-t pt-1">
                          Total Amount: ₹{(1000 + (1000 * parseFloat(rate) / 100)).toFixed(2)}
                        </div>
                      </>
                    ) : (
                      <div className="font-medium">Total Amount: ₹1,000.00 (No GST)</div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Status */}
            <div className="space-y-4">
              <FormField error={errors.is_active}>
                <Checkbox
                  {...register('is_active')}
                  label="Active"
                  description="Make this GST rate available for use"
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update GST Rate' : 'Create GST Rate')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
