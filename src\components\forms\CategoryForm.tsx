'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select, Checkbox, FileUpload } from '@/components/ui/Form';
import { useCreateCategory, useUpdateCategory, useCategories } from '@/hooks/useCategories';
import { categorySchema, CategoryFormData } from '@/lib/validations';
import { Category } from '@/types/api';

interface CategoryFormProps {
  category?: Category;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function CategoryForm({ category, isOpen, onClose, onSuccess }: CategoryFormProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const isEditing = !!category;
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory();
  const { data: categoriesResponse } = useCategories({ limit: 100 });

  const categories = categoriesResponse?.results || [];
  const parentCategories = categories.filter(cat => cat.level === 0 && cat.id !== category?.id);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      parent: undefined,
      is_active: true,
    },
  });

  // Reset form when category changes
  useEffect(() => {
    if (category) {
      reset({
        name: category.name,
        description: category.description,
        parent: category.parent || undefined,
        is_active: category.is_active,
      });
      setImagePreview(category.image || null);
    } else {
      reset();
      setImagePreview(null);
      setImageFile(null);
    }
  }, [category, reset]);

  const handleImageSelect = (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0];
      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      const formData = new FormData();
      
      // Add form fields
      Object.entries(data).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          formData.append(key, value.toString());
        }
      });

      // Add image if selected
      if (imageFile) {
        formData.append('image', imageFile);
      }

      if (isEditing && category) {
        await updateCategoryMutation.mutateAsync({
          categoryId: category.id,
          data: formData,
        });
      } else {
        await createCategoryMutation.mutateAsync(formData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'Edit Category' : 'Create New Category'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <FormField
                label="Category Name"
                required
                error={errors.name}
              >
                <Input
                  {...register('name')}
                  placeholder="Enter category name"
                  error={!!errors.name}
                />
              </FormField>

              <FormField
                label="Description"
                error={errors.description}
              >
                <Textarea
                  {...register('description')}
                  placeholder="Describe the category"
                  rows={3}
                  error={!!errors.description}
                />
              </FormField>

              <FormField
                label="Parent Category"
                error={errors.parent}
              >
                <Select
                  {...register('parent', { valueAsNumber: true })}
                  options={[
                    { value: '', label: 'None (Root Category)' },
                    ...parentCategories.map(cat => ({
                      value: cat.id,
                      label: cat.name
                    }))
                  ]}
                  placeholder="Select parent category"
                  error={!!errors.parent}
                />
              </FormField>
            </div>

            {/* Image Upload */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Category Image</h3>
              
              {imagePreview ? (
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="Category preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview(null);
                      setImageFile(null);
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <FileUpload
                  accept="image/*"
                  maxSize={5}
                  onFileSelect={handleImageSelect}
                />
              )}
            </div>

            {/* Status */}
            <div className="space-y-4">
              <FormField error={errors.is_active}>
                <Checkbox
                  {...register('is_active')}
                  label="Active"
                  description="Make this category available for services"
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Category' : 'Create Category')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
