'use client';

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, UserIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select, Checkbox, FileUpload } from '@/components/ui/Form';
import { useCreateProvider, useUpdateProvider } from '@/hooks/useProviders';
import { useServices } from '@/hooks/useServices';
import { providerSchema, ProviderFormData } from '@/lib/validations';
import { Provider } from '@/types/api';

interface ProviderFormProps {
  provider?: Provider;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function ProviderForm({ provider, isOpen, onClose, onSuccess }: ProviderFormProps) {
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);

  const isEditing = !!provider;
  const createProviderMutation = useCreateProvider();
  const updateProviderMutation = useUpdateProvider();
  const { data: servicesResponse } = useServices({ is_active: true, limit: 100 });

  const services = servicesResponse?.results || [];

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    control,
    watch,
    setValue,
  } = useForm<ProviderFormData>({
    resolver: zodResolver(providerSchema),
    defaultValues: {
      business_name: '',
      business_description: '',
      business_address: '',
      verification_status: 'pending',
      services_offered: [],
      service_areas: [''],
      is_available: true,
      accepts_new_orders: true,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'service_areas',
  });

  // Reset form when provider changes
  useEffect(() => {
    if (provider) {
      reset({
        business_name: provider.business_name || '',
        business_description: provider.business_description || '',
        business_address: provider.business_address || '',
        verification_status: provider.verification_status,
        services_offered: provider.services_offered || [],
        service_areas: provider.service_areas?.length ? provider.service_areas : [''],
        is_available: provider.is_available,
        accepts_new_orders: provider.accepts_new_orders,
      });
      setProfilePicturePreview(provider.profile_picture || null);
    } else {
      reset();
      setProfilePicturePreview(null);
      setProfilePictureFile(null);
    }
  }, [provider, reset]);

  const handleProfilePictureSelect = (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0];
      setProfilePictureFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: ProviderFormData) => {
    try {
      const formData = new FormData();
      
      // Add form fields
      Object.entries(data).forEach(([key, value]) => {
        if (key === 'services_offered' && Array.isArray(value)) {
          value.forEach(serviceId => formData.append('services_offered', serviceId.toString()));
        } else if (key === 'service_areas' && Array.isArray(value)) {
          value.filter(area => area.trim()).forEach(area => formData.append('service_areas', area));
        } else if (value !== null && value !== undefined && value !== '') {
          formData.append(key, value.toString());
        }
      });

      // Add profile picture if selected
      if (profilePictureFile) {
        formData.append('profile_picture', profilePictureFile);
      }

      if (isEditing && provider) {
        await updateProviderMutation.mutateAsync({
          providerId: provider.id,
          data: formData,
        });
      } else {
        await createProviderMutation.mutateAsync(formData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    setProfilePicturePreview(null);
    setProfilePictureFile(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit Provider' : 'Add New Provider'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Profile Picture */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Profile Picture</h3>
              
              {profilePicturePreview ? (
                <div className="relative w-24 h-24">
                  <img
                    src={profilePicturePreview}
                    alt="Profile preview"
                    className="w-24 h-24 object-cover rounded-full"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setProfilePicturePreview(null);
                      setProfilePictureFile(null);
                    }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <FileUpload
                  accept="image/*"
                  maxSize={5}
                  onFileSelect={handleProfilePictureSelect}
                />
              )}
            </div>

            {/* Business Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Business Information</h3>
              
              <FormField
                label="Business Name"
                required
                error={errors.business_name}
              >
                <Input
                  {...register('business_name')}
                  placeholder="Enter business name"
                  error={!!errors.business_name}
                />
              </FormField>

              <FormField
                label="Business Description"
                error={errors.business_description}
              >
                <Textarea
                  {...register('business_description')}
                  placeholder="Describe the business and services offered"
                  rows={3}
                  error={!!errors.business_description}
                />
              </FormField>

              <FormField
                label="Business Address"
                error={errors.business_address}
              >
                <Textarea
                  {...register('business_address')}
                  placeholder="Enter complete business address"
                  rows={2}
                  error={!!errors.business_address}
                />
              </FormField>
            </div>

            {/* Services Offered */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Services Offered</h3>
              
              <FormField
                label="Select Services"
                error={errors.services_offered}
              >
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-lg p-3">
                  {services.map(service => (
                    <label key={service.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        value={service.id}
                        {...register('services_offered')}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{service.title}</span>
                    </label>
                  ))}
                </div>
              </FormField>
            </div>

            {/* Service Areas */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Service Areas</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => append('')}
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add Area
                </Button>
              </div>

              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center space-x-2">
                  <FormField
                    label={`Area ${index + 1}`}
                    error={errors.service_areas?.[index]}
                    className="flex-1"
                  >
                    <Input
                      {...register(`service_areas.${index}`)}
                      placeholder="Enter service area (e.g., Downtown, North Zone)"
                      error={!!errors.service_areas?.[index]}
                    />
                  </FormField>
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => remove(index)}
                    >
                      <TrashIcon className="w-4 h-4 text-red-500" />
                    </Button>
                  )}
                </div>
              ))}
            </div>

            {/* Status and Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Status & Settings</h3>
              
              <FormField
                label="Verification Status"
                required
                error={errors.verification_status}
              >
                <Select
                  {...register('verification_status')}
                  options={[
                    { value: 'pending', label: 'Pending Verification' },
                    { value: 'verified', label: 'Verified' },
                    { value: 'rejected', label: 'Rejected' }
                  ]}
                  error={!!errors.verification_status}
                />
              </FormField>

              <div className="flex space-x-6">
                <FormField error={errors.is_available}>
                  <Checkbox
                    {...register('is_available')}
                    label="Available for Orders"
                    description="Provider can receive new order assignments"
                  />
                </FormField>

                <FormField error={errors.accepts_new_orders}>
                  <Checkbox
                    {...register('accepts_new_orders')}
                    label="Accepts New Orders"
                    description="Provider is accepting new customer orders"
                  />
                </FormField>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Provider' : 'Add Provider')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
