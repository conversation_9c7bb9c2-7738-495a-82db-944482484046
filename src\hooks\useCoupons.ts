import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { couponsApi } from '@/lib/api';
import { Coupon, PaginatedResponse } from '@/types/api';

// Query keys
export const couponKeys = {
  all: ['coupons'] as const,
  lists: () => [...couponKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...couponKeys.lists(), params] as const,
  details: () => [...couponKeys.all, 'detail'] as const,
  detail: (couponId: number) => [...couponKeys.details(), couponId] as const,
};

// Get coupons list with filters
export const useCoupons = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: couponKeys.list(params),
    queryFn: () => couponsApi.getCoupons(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single coupon details
export const useCoupon = (couponId: number) => {
  return useQuery({
    queryKey: couponKeys.detail(couponId),
    queryFn: () => couponsApi.getCouponDetail(couponId),
    enabled: !!couponId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Create coupon mutation
export const useCreateCoupon = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => couponsApi.createCoupon(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: couponKeys.lists() });
      toast.success('Coupon created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create coupon');
    },
  });
};

// Update coupon mutation
export const useUpdateCoupon = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ couponId, data }: { couponId: number; data: any }) =>
      couponsApi.updateCoupon(couponId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: couponKeys.lists() });
      queryClient.invalidateQueries({ queryKey: couponKeys.detail(variables.couponId) });
      toast.success('Coupon updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update coupon');
    },
  });
};

// Delete coupon mutation
export const useDeleteCoupon = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (couponId: number) => couponsApi.deleteCoupon(couponId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: couponKeys.lists() });
      toast.success('Coupon deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete coupon');
    },
  });
};

// Toggle coupon status mutation
export const useToggleCouponStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ couponId, isActive }: { couponId: number; isActive: boolean }) =>
      couponsApi.updateCoupon(couponId, { is_active: isActive }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: couponKeys.lists() });
      toast.success('Coupon status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update coupon status');
    },
  });
};
