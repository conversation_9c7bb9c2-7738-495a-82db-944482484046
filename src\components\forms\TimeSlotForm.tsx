'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, ClockIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select } from '@/components/ui/Form';
import { useCreateTimeSlot, useUpdateTimeSlot } from '@/hooks/useScheduling';
import { timeSlotSchema, TimeSlotFormData } from '@/lib/validations';
import { TimeSlot } from '@/types/api';

interface TimeSlotFormProps {
  timeSlot?: TimeSlot;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function TimeSlotForm({ timeSlot, isOpen, onClose, onSuccess }: TimeSlotFormProps) {
  const isEditing = !!timeSlot;
  const createTimeSlotMutation = useCreateTimeSlot();
  const updateTimeSlotMutation = useUpdateTimeSlot();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
  } = useForm<TimeSlotFormData>({
    resolver: zodResolver(timeSlotSchema),
    defaultValues: {
      date: '',
      start_time: '',
      end_time: '',
      max_bookings: 1,
      status: 'available',
      blocked_reason: '',
      notes: '',
    },
  });

  const status = watch('status');

  // Reset form when timeSlot changes
  useEffect(() => {
    if (timeSlot) {
      reset({
        date: timeSlot.date,
        start_time: timeSlot.start_time,
        end_time: timeSlot.end_time,
        max_bookings: timeSlot.max_bookings,
        status: timeSlot.status,
        blocked_reason: timeSlot.blocked_reason || '',
        notes: timeSlot.notes || '',
      });
    } else {
      reset();
    }
  }, [timeSlot, reset]);

  const onSubmit = async (data: TimeSlotFormData) => {
    try {
      // Clean up data based on status
      const cleanedData = {
        ...data,
        blocked_reason: data.status === 'blocked' ? data.blocked_reason : undefined,
        notes: data.notes || undefined,
      };

      if (isEditing && timeSlot) {
        await updateTimeSlotMutation.mutateAsync({
          slotId: timeSlot.id,
          data: cleanedData,
        });
      } else {
        await createTimeSlotMutation.mutateAsync(cleanedData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <ClockIcon className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit Time Slot' : 'Create New Time Slot'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Date and Time */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Schedule Details</h3>
              
              <FormField
                label="Date"
                required
                error={errors.date}
              >
                <Input
                  {...register('date')}
                  type="date"
                  min={new Date().toISOString().split('T')[0]}
                  error={!!errors.date}
                />
              </FormField>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Start Time"
                  required
                  error={errors.start_time}
                >
                  <Input
                    {...register('start_time')}
                    type="time"
                    error={!!errors.start_time}
                  />
                </FormField>

                <FormField
                  label="End Time"
                  required
                  error={errors.end_time}
                >
                  <Input
                    {...register('end_time')}
                    type="time"
                    error={!!errors.end_time}
                  />
                </FormField>
              </div>
            </div>

            {/* Capacity and Status */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Capacity & Status</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Maximum Bookings"
                  required
                  error={errors.max_bookings}
                >
                  <Input
                    {...register('max_bookings', { valueAsNumber: true })}
                    type="number"
                    min="1"
                    max="50"
                    placeholder="1"
                    error={!!errors.max_bookings}
                  />
                </FormField>

                <FormField
                  label="Status"
                  required
                  error={errors.status}
                >
                  <Select
                    {...register('status')}
                    options={[
                      { value: 'available', label: 'Available' },
                      { value: 'booked', label: 'Fully Booked' },
                      { value: 'blocked', label: 'Blocked' }
                    ]}
                    error={!!errors.status}
                  />
                </FormField>
              </div>

              {status === 'blocked' && (
                <FormField
                  label="Block Reason"
                  required
                  error={errors.blocked_reason}
                >
                  <Select
                    {...register('blocked_reason')}
                    options={[
                      { value: '', label: 'Select a reason...' },
                      { value: 'maintenance', label: 'Maintenance' },
                      { value: 'holiday', label: 'Holiday' },
                      { value: 'staff_unavailable', label: 'Staff Unavailable' },
                      { value: 'emergency', label: 'Emergency' },
                      { value: 'other', label: 'Other' }
                    ]}
                    error={!!errors.blocked_reason}
                  />
                </FormField>
              )}
            </div>

            {/* Additional Notes */}
            <div className="space-y-4">
              <FormField
                label="Notes (Optional)"
                error={errors.notes}
              >
                <Textarea
                  {...register('notes')}
                  placeholder="Add any additional notes about this time slot..."
                  rows={3}
                  error={!!errors.notes}
                />
              </FormField>
            </div>

            {/* Status Information */}
            <div className={`p-4 rounded-lg ${
              status === 'available' 
                ? 'bg-green-50 border border-green-200' 
                : status === 'blocked'
                ? 'bg-red-50 border border-red-200'
                : 'bg-yellow-50 border border-yellow-200'
            }`}>
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  status === 'available' ? 'bg-green-500' : 
                  status === 'blocked' ? 'bg-red-500' : 'bg-yellow-500'
                }`}></div>
                <span className={`text-sm font-medium ${
                  status === 'available' ? 'text-green-700' : 
                  status === 'blocked' ? 'text-red-700' : 'text-yellow-700'
                }`}>
                  {status === 'available' && 'This slot will be available for bookings'}
                  {status === 'blocked' && 'This slot will be blocked and unavailable for bookings'}
                  {status === 'booked' && 'This slot is marked as fully booked'}
                </span>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Time Slot' : 'Create Time Slot')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
