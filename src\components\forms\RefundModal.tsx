'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, ArrowPathIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select } from '@/components/ui/Form';
import { formatCurrency } from '@/lib/utils';
import { PaymentTransaction } from '@/types/api';
import { z } from 'zod';

const refundSchema = z.object({
  amount: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid amount').optional(),
  refund_type: z.enum(['full', 'partial']),
  reason: z.string().min(1, 'Refund reason is required'),
  admin_notes: z.string().optional(),
});

type RefundFormData = z.infer<typeof refundSchema>;

interface RefundModalProps {
  transaction: PaymentTransaction | null;
  isOpen: boolean;
  onClose: () => void;
  onRefund: (data: { amount?: string; reason: string; admin_notes?: string }) => void;
  isLoading?: boolean;
}

export default function RefundModal({
  transaction,
  isOpen,
  onClose,
  onRefund,
  isLoading = false
}: RefundModalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<RefundFormData>({
    resolver: zodResolver(refundSchema),
    defaultValues: {
      refund_type: 'full',
      reason: '',
      amount: '',
      admin_notes: '',
    },
  });

  const refundType = watch('refund_type');

  React.useEffect(() => {
    if (transaction && refundType === 'full') {
      setValue('amount', transaction.amount);
    }
  }, [transaction, refundType, setValue]);

  const onSubmit = async (data: RefundFormData) => {
    try {
      const refundData = {
        reason: data.reason,
        admin_notes: data.admin_notes,
        ...(data.refund_type === 'partial' && data.amount ? { amount: data.amount } : {})
      };
      
      await onRefund(refundData);
      reset();
      onClose();
    } catch (error) {
      // Error is handled by the parent component
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen || !transaction) return null;

  const maxRefundAmount = parseFloat(transaction.amount);

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-orange-100 rounded flex items-center justify-center">
                <ArrowPathIcon className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Process Refund
                </h2>
                <p className="text-sm text-gray-500">
                  Transaction: {transaction.transaction_id}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Transaction Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Transaction Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Order:</span>
                  <span className="ml-2 text-gray-900">{transaction.order_number}</span>
                </div>
                <div>
                  <span className="text-gray-600">Amount:</span>
                  <span className="ml-2 text-gray-900 font-medium">{formatCurrency(transaction.amount)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Method:</span>
                  <span className="ml-2 text-gray-900 capitalize">{transaction.payment_method}</span>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <span className="ml-2 text-gray-900 capitalize">{transaction.status}</span>
                </div>
              </div>
            </div>

            {/* Warning */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400 mt-0.5 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Important Notice</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This action cannot be undone. The refund will be processed immediately and 
                    the customer will be notified via email.
                  </p>
                </div>
              </div>
            </div>

            {/* Refund Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Refund Configuration</h3>
              
              <FormField
                label="Refund Type"
                required
                error={errors.refund_type}
              >
                <Select
                  {...register('refund_type')}
                  options={[
                    { value: 'full', label: `Full Refund (${formatCurrency(transaction.amount)})` },
                    { value: 'partial', label: 'Partial Refund' }
                  ]}
                  error={!!errors.refund_type}
                />
              </FormField>

              {refundType === 'partial' && (
                <FormField
                  label="Refund Amount (₹)"
                  required
                  error={errors.amount}
                >
                  <Input
                    {...register('amount')}
                    type="number"
                    step="0.01"
                    min="0.01"
                    max={maxRefundAmount.toString()}
                    placeholder="0.00"
                    error={!!errors.amount}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum refundable amount: {formatCurrency(transaction.amount)}
                  </p>
                </FormField>
              )}

              <FormField
                label="Refund Reason"
                required
                error={errors.reason}
              >
                <Select
                  {...register('reason')}
                  options={[
                    { value: '', label: 'Select a reason...' },
                    { value: 'customer_request', label: 'Customer Request' },
                    { value: 'service_cancelled', label: 'Service Cancelled' },
                    { value: 'service_not_delivered', label: 'Service Not Delivered' },
                    { value: 'quality_issues', label: 'Quality Issues' },
                    { value: 'billing_error', label: 'Billing Error' },
                    { value: 'duplicate_payment', label: 'Duplicate Payment' },
                    { value: 'other', label: 'Other' }
                  ]}
                  error={!!errors.reason}
                />
              </FormField>

              <FormField
                label="Admin Notes (Optional)"
                error={errors.admin_notes}
              >
                <Textarea
                  {...register('admin_notes')}
                  placeholder="Add any internal notes about this refund..."
                  rows={3}
                  error={!!errors.admin_notes}
                />
              </FormField>
            </div>

            {/* Refund Summary */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Refund Summary</h4>
              <div className="text-sm text-blue-700">
                <div className="flex justify-between">
                  <span>Original Amount:</span>
                  <span>{formatCurrency(transaction.amount)}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Refund Amount:</span>
                  <span>
                    {refundType === 'full' 
                      ? formatCurrency(transaction.amount)
                      : watch('amount') 
                        ? formatCurrency(watch('amount'))
                        : '₹0.00'
                    }
                  </span>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting || isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="danger"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? 'Processing...' : 'Process Refund'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
