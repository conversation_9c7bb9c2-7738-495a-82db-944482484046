'use client';

import React, { useState, useEffect } from 'react';
import {
  CogIcon,
  BellIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select, Checkbox } from '@/components/ui/Form';
import {
  useGeneralSettings,
  useBusinessSettings,
  useNotificationSettings,
  useSecuritySettings,
  useIntegrationSettings,
  useUpdateGeneralSettings,
  useUpdateBusinessSettings,
  useUpdateNotificationSettings,
  useUpdateSecuritySettings,
  useUpdateIntegrationSettings,
  useTestIntegration
} from '@/hooks/useSettings';
import { useForm } from 'react-hook-form';
import ApiDebugger from '@/components/debug/ApiDebugger';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'general' | 'notifications' | 'security' | 'integrations' | 'business'>('general');

  // API hooks
  const { data: generalSettings, isLoading: generalLoading } = useGeneralSettings();
  const { data: businessSettings, isLoading: businessLoading } = useBusinessSettings();
  const { data: notificationSettings, isLoading: notificationLoading } = useNotificationSettings();
  const { data: securitySettings, isLoading: securityLoading } = useSecuritySettings();
  const { data: integrationSettings, isLoading: integrationLoading } = useIntegrationSettings();

  // Mutation hooks
  const updateGeneralMutation = useUpdateGeneralSettings();
  const updateBusinessMutation = useUpdateBusinessSettings();
  const updateNotificationMutation = useUpdateNotificationSettings();
  const updateSecurityMutation = useUpdateSecuritySettings();
  const updateIntegrationMutation = useUpdateIntegrationSettings();
  const testIntegrationMutation = useTestIntegration();

  // Form hooks
  const generalForm = useForm({
    defaultValues: {
      app_name: '',
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      date_format: 'DD/MM/YYYY',
    },
  });

  const businessForm = useForm({
    defaultValues: {
      business_name: '',
      contact_email: '',
      contact_phone: '',
      business_address: '',
      gst_number: '',
      pan_number: '',
    },
  });

  const notificationForm = useForm({
    defaultValues: {
      email_notifications: true,
      sms_notifications: true,
      new_order_alerts: true,
      payment_notifications: true,
    },
  });

  const securityForm = useForm({
    defaultValues: {
      min_password_length: 8,
      require_uppercase: true,
      require_numbers: true,
      require_special_chars: false,
      session_timeout: 30,
      force_logout_on_password_change: true,
    },
  });

  // Update form defaults when data loads
  useEffect(() => {
    if (generalSettings) {
      generalForm.reset(generalSettings);
    }
  }, [generalSettings, generalForm]);

  useEffect(() => {
    if (businessSettings) {
      businessForm.reset(businessSettings);
    }
  }, [businessSettings, businessForm]);

  useEffect(() => {
    if (notificationSettings) {
      notificationForm.reset(notificationSettings);
    }
  }, [notificationSettings, notificationForm]);

  useEffect(() => {
    if (securitySettings) {
      securityForm.reset(securitySettings);
    }
  }, [securitySettings, securityForm]);

  // Form submit handlers
  const handleGeneralSubmit = (data: any) => {
    updateGeneralMutation.mutate(data);
  };

  const handleBusinessSubmit = (data: any) => {
    updateBusinessMutation.mutate(data);
  };

  const handleNotificationSubmit = (data: any) => {
    updateNotificationMutation.mutate(data);
  };

  const handleSecuritySubmit = (data: any) => {
    updateSecurityMutation.mutate(data);
  };

  const handleTestIntegration = (integration: string, config: any) => {
    testIntegrationMutation.mutate({ integration, config });
  };

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'business', name: 'Business Info', icon: BuildingOfficeIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'integrations', name: 'Integrations', icon: GlobeAltIcon },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        </div>



        <div className="bg-white rounded-lg shadow">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'general' && (
              <form onSubmit={generalForm.handleSubmit(handleGeneralSubmit)} className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">General Settings</h3>

                {generalLoading ? (
                  <div className="text-center py-4">Loading...</div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      label="Application Name"
                      required
                      error={generalForm.formState.errors.app_name}
                    >
                      <Input
                        {...generalForm.register('app_name', { required: 'Application name is required' })}
                        placeholder="Home Services Admin"
                        error={!!generalForm.formState.errors.app_name}
                      />
                    </FormField>

                    <FormField
                      label="Default Currency"
                      error={generalForm.formState.errors.currency}
                    >
                      <Select
                        {...generalForm.register('currency')}
                        options={[
                          { value: 'INR', label: 'Indian Rupee (₹)' },
                          { value: 'USD', label: 'US Dollar ($)' },
                          { value: 'EUR', label: 'Euro (€)' },
                        ]}
                        error={!!generalForm.formState.errors.currency}
                      />
                    </FormField>

                    <FormField
                      label="Timezone"
                      error={generalForm.formState.errors.timezone}
                    >
                      <Select
                        {...generalForm.register('timezone')}
                        options={[
                          { value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
                          { value: 'America/New_York', label: 'America/New_York (EST)' },
                          { value: 'Europe/London', label: 'Europe/London (GMT)' },
                        ]}
                        error={!!generalForm.formState.errors.timezone}
                      />
                    </FormField>

                    <FormField
                      label="Date Format"
                      error={generalForm.formState.errors.date_format}
                    >
                      <Select
                        {...generalForm.register('date_format')}
                        options={[
                          { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
                          { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
                          { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
                        ]}
                        error={!!generalForm.formState.errors.date_format}
                      />
                    </FormField>
                  </div>
                )}

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={updateGeneralMutation.isPending}
                  >
                    {updateGeneralMutation.isPending ? 'Saving...' : 'Save General Settings'}
                  </Button>
                </div>
              </form>
            )}

            {activeTab === 'business' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Business Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Business Name
                    </label>
                    <input
                      type="text"
                      defaultValue="Home Services Company"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <EnvelopeIcon className="w-4 h-4 inline mr-1" />
                      Contact Email
                    </label>
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <PhoneIcon className="w-4 h-4 inline mr-1" />
                      Contact Phone
                    </label>
                    <input
                      type="tel"
                      defaultValue="+91 9876543210"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <MapPinIcon className="w-4 h-4 inline mr-1" />
                      Business Address
                    </label>
                    <textarea
                      rows={3}
                      defaultValue="123 Business Street, City, State, PIN - 123456"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      GST Number
                    </label>
                    <input
                      type="text"
                      placeholder="22**********1Z5"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      PAN Number
                    </label>
                    <input
                      type="text"
                      placeholder="**********"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button>Save Business Info</Button>
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Notification Settings</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                      <p className="text-sm text-gray-500">Receive notifications via email</p>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">SMS Notifications</h4>
                      <p className="text-sm text-gray-500">Receive notifications via SMS</p>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">New Order Alerts</h4>
                      <p className="text-sm text-gray-500">Get notified when new orders are placed</p>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Payment Notifications</h4>
                      <p className="text-sm text-gray-500">Get notified about payment updates</p>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button>Save Notification Settings</Button>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>

                <div className="space-y-6">
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Password Policy</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Minimum password length</span>
                        <input type="number" defaultValue="8" min="6" max="20" className="w-20 px-2 py-1 border border-gray-300 rounded" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Require uppercase letters</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Require numbers</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Require special characters</span>
                        <input type="checkbox" className="rounded" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Session Management</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Session timeout (minutes)</span>
                        <input type="number" defaultValue="30" min="5" max="480" className="w-20 px-2 py-1 border border-gray-300 rounded" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Force logout on password change</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button>Save Security Settings</Button>
                </div>
              </div>
            )}

            {activeTab === 'integrations' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Third-party Integrations</h3>

                <div className="space-y-6">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-md font-medium text-gray-900">Razorpay Payment Gateway</h4>
                      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">Connected</span>
                    </div>
                    <p className="text-sm text-gray-500 mb-3">Process online payments securely</p>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-md font-medium text-gray-900">SMS Gateway</h4>
                      <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Not Connected</span>
                    </div>
                    <p className="text-sm text-gray-500 mb-3">Send SMS notifications to customers</p>
                    <Button variant="outline" size="sm">Setup</Button>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-md font-medium text-gray-900">Email Service</h4>
                      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">Connected</span>
                    </div>
                    <p className="text-sm text-gray-500 mb-3">Send email notifications and receipts</p>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
