'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { CogIcon, CreditCardIcon, BanknotesIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Select, Checkbox } from '@/components/ui/Form';
import { usePaymentConfig, useUpdatePaymentConfig } from '@/hooks/usePayments';
import { paymentConfigSchema, PaymentConfigFormData } from '@/lib/validations';

export default function PaymentConfigForm() {
  const { data: paymentConfig, isLoading } = usePaymentConfig();
  const updateConfigMutation = useUpdatePaymentConfig();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
  } = useForm<PaymentConfigFormData>({
    resolver: zodResolver(paymentConfigSchema),
    defaultValues: {
      razorpay_test_key_id: '',
      razorpay_test_key_secret: '',
      razorpay_live_key_id: '',
      razorpay_live_key_secret: '',
      active_environment: 'test',
      enable_razorpay: true,
      enable_cod: true,
      cod_charge_percentage: '2.50',
      cod_minimum_order: '100.00',
    },
  });

  const activeEnvironment = watch('active_environment');

  // Reset form when config data loads
  useEffect(() => {
    if (paymentConfig) {
      reset({
        razorpay_test_key_id: paymentConfig.razorpay_test_key_id || '',
        razorpay_test_key_secret: paymentConfig.razorpay_test_key_secret || '',
        razorpay_live_key_id: paymentConfig.razorpay_live_key_id || '',
        razorpay_live_key_secret: paymentConfig.razorpay_live_key_secret || '',
        active_environment: paymentConfig.active_environment || 'test',
        enable_razorpay: paymentConfig.enable_razorpay ?? true,
        enable_cod: paymentConfig.enable_cod ?? true,
        cod_charge_percentage: paymentConfig.cod_charge_percentage?.toString() || '2.50',
        cod_minimum_order: paymentConfig.cod_minimum_order?.toString() || '100.00',
      });
    }
  }, [paymentConfig, reset]);

  const onSubmit = async (data: PaymentConfigFormData) => {
    try {
      await updateConfigMutation.mutateAsync(data);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
            <CogIcon className="w-5 h-5 text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900">Payment Configuration</h3>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
        {/* Environment Selection */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 flex items-center">
            <CreditCardIcon className="w-5 h-5 mr-2" />
            Environment Settings
          </h4>
          
          <FormField
            label="Active Environment"
            required
            error={errors.active_environment}
          >
            <Select
              {...register('active_environment')}
              options={[
                { value: 'test', label: 'Test Environment' },
                { value: 'live', label: 'Live Environment' }
              ]}
              error={!!errors.active_environment}
            />
          </FormField>

          <div className="flex space-x-6">
            <FormField error={errors.enable_razorpay}>
              <Checkbox
                {...register('enable_razorpay')}
                label="Enable Razorpay"
                description="Allow online payments through Razorpay"
              />
            </FormField>

            <FormField error={errors.enable_cod}>
              <Checkbox
                {...register('enable_cod')}
                label="Enable Cash on Delivery"
                description="Allow cash payments on delivery"
              />
            </FormField>
          </div>
        </div>

        {/* Razorpay Configuration */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900">Razorpay Settings</h4>
          
          {/* Test Environment Keys */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-gray-700 mb-3">Test Environment</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Test Key ID"
                error={errors.razorpay_test_key_id}
              >
                <Input
                  {...register('razorpay_test_key_id')}
                  placeholder="rzp_test_..."
                  error={!!errors.razorpay_test_key_id}
                />
              </FormField>

              <FormField
                label="Test Key Secret"
                error={errors.razorpay_test_key_secret}
              >
                <Input
                  {...register('razorpay_test_key_secret')}
                  type="password"
                  placeholder="••••••••"
                  error={!!errors.razorpay_test_key_secret}
                />
              </FormField>
            </div>
          </div>

          {/* Live Environment Keys */}
          <div className="bg-red-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-red-700 mb-3">Live Environment (Production)</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Live Key ID"
                error={errors.razorpay_live_key_id}
              >
                <Input
                  {...register('razorpay_live_key_id')}
                  placeholder="rzp_live_..."
                  error={!!errors.razorpay_live_key_id}
                />
              </FormField>

              <FormField
                label="Live Key Secret"
                error={errors.razorpay_live_key_secret}
              >
                <Input
                  {...register('razorpay_live_key_secret')}
                  type="password"
                  placeholder="••••••••"
                  error={!!errors.razorpay_live_key_secret}
                />
              </FormField>
            </div>
            <p className="text-xs text-red-600 mt-2">
              ⚠️ Live keys should only be used in production environment
            </p>
          </div>
        </div>

        {/* COD Configuration */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 flex items-center">
            <BanknotesIcon className="w-5 h-5 mr-2" />
            Cash on Delivery Settings
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="COD Charge (%)"
              required
              error={errors.cod_charge_percentage}
            >
              <Input
                {...register('cod_charge_percentage')}
                type="number"
                step="0.01"
                min="0"
                max="100"
                placeholder="2.50"
                error={!!errors.cod_charge_percentage}
              />
            </FormField>

            <FormField
              label="Minimum Order Amount (₹)"
              required
              error={errors.cod_minimum_order}
            >
              <Input
                {...register('cod_minimum_order')}
                type="number"
                step="0.01"
                min="0"
                placeholder="100.00"
                error={!!errors.cod_minimum_order}
              />
            </FormField>
          </div>
        </div>

        {/* Current Environment Indicator */}
        <div className={`p-4 rounded-lg ${
          activeEnvironment === 'live' 
            ? 'bg-red-50 border border-red-200' 
            : 'bg-green-50 border border-green-200'
        }`}>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              activeEnvironment === 'live' ? 'bg-red-500' : 'bg-green-500'
            }`}></div>
            <span className={`text-sm font-medium ${
              activeEnvironment === 'live' ? 'text-red-700' : 'text-green-700'
            }`}>
              Currently using {activeEnvironment.toUpperCase()} environment
            </span>
          </div>
          <p className={`text-xs mt-1 ${
            activeEnvironment === 'live' ? 'text-red-600' : 'text-green-600'
          }`}>
            {activeEnvironment === 'live' 
              ? 'Real transactions will be processed. Use with caution.'
              : 'Test transactions only. No real money will be charged.'
            }
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-6 border-t">
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save Configuration'}
          </Button>
        </div>
      </form>
    </div>
  );
}
