'use client';

import React, { useState, useMemo } from 'react';
import {
  CalendarIcon,
  ClockIcon,
  UserIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { formatDateTime, formatRelativeTime } from '@/lib/utils';
import { useBookings, useUpdateBookingStatus, useRescheduleBooking } from '@/hooks/useScheduling';
import { Booking } from '@/types/api';
import TimeSlotForm from '@/components/forms/TimeSlotForm';
import BookingDetailsModal from '@/components/forms/BookingDetailsModal';
import ApiDebugger from '@/components/debug/ApiDebugger';

// Mock Booking type for now
interface Booking {
  id: number;
  order_id: string;
  customer_name: string;
  customer_mobile: string;
  provider_name?: string;
  service_names: string[];
  booking_status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  scheduled_date: string;
  scheduled_time: string;
  quantity: number;
  booked_at: string;
  confirmed_at?: string;
  cancelled_at?: string;
  completed_at?: string;
  customer_notes?: string;
  provider_notes?: string;
}

// Mock data for now
const mockBookings: Booking[] = [
  {
    id: 1,
    order_id: 'ORD_001',
    customer_name: 'John Doe',
    customer_mobile: '+919876543210',
    provider_name: 'ABC Services',
    service_names: ['Home Cleaning', 'Kitchen Deep Clean'],
    booking_status: 'confirmed',
    scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    scheduled_time: '10:00',
    quantity: 1,
    booked_at: new Date().toISOString(),
    confirmed_at: new Date().toISOString(),
    customer_notes: 'Please call before arriving',
  },
  {
    id: 2,
    order_id: 'ORD_002',
    customer_name: 'Jane Smith',
    customer_mobile: '+919876543211',
    service_names: ['Plumbing Repair'],
    booking_status: 'pending',
    scheduled_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    scheduled_time: '14:00',
    quantity: 1,
    booked_at: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
    customer_notes: 'Kitchen sink is leaking',
  },
];

export default function SchedulingPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [page, setPage] = useState(1);
  const [viewMode, setViewMode] = useState<'table' | 'calendar'>('table');
  const [showTimeSlotModal, setShowTimeSlotModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showBookingDetails, setShowBookingDetails] = useState(false);

  // API calls
  const updateBookingStatusMutation = useUpdateBookingStatus();
  const rescheduleBookingMutation = useRescheduleBooking();

  const { data: bookingsResponse, isLoading } = useBookings({
    page,
    search: searchTerm,
    status: statusFilter,
    date_filter: dateFilter,
  });

  const bookings = bookingsResponse?.results || [];
  const totalItems = bookingsResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle booking actions
  const handleViewBooking = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowBookingDetails(true);
  };

  const handleUpdateBookingStatus = (booking: Booking, status: string) => {
    updateBookingStatusMutation.mutate({
      bookingId: booking.id,
      status,
    });
  };

  const handleRescheduleBooking = (booking: Booking) => {
    // This would open a reschedule modal
    console.log('Reschedule booking:', booking.id);
  };

  const handleCloseModals = () => {
    setShowTimeSlotModal(false);
    setSelectedBooking(null);
    setShowBookingDetails(false);
  };

  // Define table columns
  const columns: Column<Booking>[] = [
    {
      key: 'order_id',
      label: 'Booking',
      sortable: true,
      render: (_, booking) => (
        <div>
          <div className="font-medium text-gray-900">#{booking.order_id}</div>
          <div className="text-sm text-gray-500">
            {booking.service_names.join(', ')}
          </div>
        </div>
      ),
    },
    {
      key: 'customer_name',
      label: 'Customer',
      render: (_, booking) => (
        <div>
          <div className="font-medium text-gray-900">{booking.customer_name}</div>
          <div className="text-sm text-gray-500">{booking.customer_mobile}</div>
        </div>
      ),
    },
    {
      key: 'provider_name',
      label: 'Provider',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {value || 'Not assigned'}
        </div>
      ),
    },
    {
      key: 'scheduled_date',
      label: 'Scheduled',
      sortable: true,
      render: (_, booking) => (
        <div>
          <div className="font-medium text-gray-900">
            {new Date(booking.scheduled_date).toLocaleDateString()}
          </div>
          <div className="text-sm text-gray-500">
            {booking.scheduled_time}
          </div>
        </div>
      ),
    },
    {
      key: 'booking_status',
      label: 'Status',
      render: (value) => (
        <Badge
          variant={
            value === 'completed' ? 'success' :
            value === 'cancelled' ? 'danger' :
            value === 'confirmed' ? 'info' :
            'warning'
          }
        >
          {value}
        </Badge>
      ),
    },
    {
      key: 'booked_at',
      label: 'Booked',
      sortable: true,
      render: (value) => (
        <div>
          <div className="text-sm text-gray-900">{formatDateTime(value)}</div>
          <div className="text-xs text-gray-500">{formatRelativeTime(value)}</div>
        </div>
      ),
    },
  ];

  // Define row actions
  const rowActions = (booking: Booking) => (
    <div className="flex items-center space-x-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleViewBooking(booking)}
        title="View Details"
      >
        <EyeIcon className="w-4 h-4" />
      </Button>

      {booking.booking_status === 'pending' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleUpdateBookingStatus(booking, 'confirmed')}
          title="Confirm Booking"
        >
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        </Button>
      )}

      {booking.booking_status === 'confirmed' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleUpdateBookingStatus(booking, 'completed')}
          title="Mark Complete"
        >
          <CheckCircleIcon className="w-4 h-4 text-blue-500" />
        </Button>
      )}

      {booking.booking_status !== 'completed' && booking.booking_status !== 'cancelled' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleUpdateBookingStatus(booking, 'cancelled')}
          title="Cancel Booking"
        >
          <XCircleIcon className="w-4 h-4 text-red-500" />
        </Button>
      )}

      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleRescheduleBooking(booking)}
        title="Reschedule"
      >
        <ClockIcon className="w-4 h-4 text-orange-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Schedule Management</h1>
          <div className="flex items-center space-x-3">
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                  viewMode === 'table'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Table View
              </button>
              <button
                onClick={() => setViewMode('calendar')}
                className={`px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                  viewMode === 'calendar'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Calendar View
              </button>
            </div>
            <Button onClick={() => setShowTimeSlotModal(true)}>
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Time Slot
            </Button>
          </div>
        </div>



        {viewMode === 'table' && (
          <>
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Booking Status
                  </label>
                  <select
                    value={statusFilter}
                    onChange={(e) => {
                      setStatusFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Range
                  </label>
                  <select
                    value={dateFilter}
                    onChange={(e) => {
                      setDateFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Dates</option>
                    <option value="today">Today</option>
                    <option value="tomorrow">Tomorrow</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Bookings Table */}
            <DataTable
              data={bookings}
              columns={columns}
              loading={isLoading}
              searchable
              searchPlaceholder="Search bookings by order, customer, or service..."
              onSearch={handleSearch}
              sortable
              pagination={{
                page,
                totalPages,
                totalItems,
                itemsPerPage: 20,
                onPageChange: setPage,
              }}
              actions={rowActions}
              emptyState={{
                title: 'No bookings found',
                description: 'No bookings match your current filters.',
                action: (
                  <div className="space-y-3">
                    <Button onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('');
                      setDateFilter('');
                      setPage(1);
                    }}>
                      Clear Filters
                    </Button>
                    <div className="text-sm text-gray-500">or</div>
                    <Button onClick={() => setShowTimeSlotModal(true)}>
                      <PlusIcon className="w-4 h-4 mr-2" />
                      Create First Time Slot
                    </Button>
                  </div>
                ),
              }}
            />
          </>
        )}

        {viewMode === 'calendar' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Calendar View</h3>
            <div className="text-center py-12 text-gray-500">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>Calendar view will be implemented here</p>
              <p className="text-sm">This will show a monthly/weekly calendar with bookings</p>
            </div>
          </div>
        )}

        {/* Time Slot Form Modal */}
        <TimeSlotForm
          isOpen={showTimeSlotModal}
          onClose={handleCloseModals}
          onSuccess={handleCloseModals}
        />

        {/* Booking Details Modal */}
        <BookingDetailsModal
          booking={selectedBooking}
          isOpen={showBookingDetails}
          onClose={handleCloseModals}
          onUpdateStatus={handleUpdateBookingStatus}
          onReschedule={handleRescheduleBooking}
        />
      </div>
    </DashboardLayout>
  );
}
