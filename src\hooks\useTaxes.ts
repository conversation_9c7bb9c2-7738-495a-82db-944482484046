import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { taxesApi } from '@/lib/api';
import { TaxCategory, GSTRate, PaginatedResponse } from '@/types/api';

// Query keys
export const taxKeys = {
  all: ['taxes'] as const,
  categories: () => [...taxKeys.all, 'categories'] as const,
  category: (params: Record<string, any>) => [...taxKeys.categories(), params] as const,
  gstRates: () => [...taxKeys.all, 'gst-rates'] as const,
  gstRate: (params: Record<string, any>) => [...taxKeys.gstRates(), params] as const,
  details: () => [...taxKeys.all, 'detail'] as const,
  detail: (id: number) => [...taxKeys.details(), id] as const,
  reports: () => [...taxKeys.all, 'reports'] as const,
};

// Get tax categories with filters
export const useTaxCategories = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: taxKeys.category(params),
    queryFn: () => taxesApi.getTaxCategories(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get GST rates with filters
export const useGSTRates = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: taxKeys.gstRate(params),
    queryFn: () => taxesApi.getGSTRates(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get tax reports
export const useTaxReports = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: taxKeys.reports(),
    queryFn: () => taxesApi.getTaxReports(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Create tax category mutation
export const useCreateTaxCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => taxesApi.createTaxCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taxKeys.categories() });
      toast.success('Tax category created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create tax category');
    },
  });
};

// Update tax category mutation
export const useUpdateTaxCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, data }: { categoryId: number; data: any }) =>
      taxesApi.updateTaxCategory(categoryId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: taxKeys.categories() });
      queryClient.invalidateQueries({ queryKey: taxKeys.detail(variables.categoryId) });
      toast.success('Tax category updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update tax category');
    },
  });
};

// Delete tax category mutation
export const useDeleteTaxCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (categoryId: number) => taxesApi.deleteTaxCategory(categoryId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taxKeys.categories() });
      toast.success('Tax category deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete tax category');
    },
  });
};

// Create GST rate mutation
export const useCreateGSTRate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => taxesApi.createGSTRate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taxKeys.gstRates() });
      toast.success('GST rate created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create GST rate');
    },
  });
};

// Update GST rate mutation
export const useUpdateGSTRate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ rateId, data }: { rateId: number; data: any }) =>
      taxesApi.updateGSTRate(rateId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: taxKeys.gstRates() });
      queryClient.invalidateQueries({ queryKey: taxKeys.detail(variables.rateId) });
      toast.success('GST rate updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update GST rate');
    },
  });
};

// Delete GST rate mutation
export const useDeleteGSTRate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (rateId: number) => taxesApi.deleteGSTRate(rateId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taxKeys.gstRates() });
      toast.success('GST rate deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete GST rate');
    },
  });
};

// Toggle tax category status mutation
export const useToggleTaxCategoryStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, isActive }: { categoryId: number; isActive: boolean }) =>
      taxesApi.toggleTaxCategoryStatus(categoryId, { is_active: isActive }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taxKeys.categories() });
      toast.success('Tax category status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update tax category status');
    },
  });
};

// Toggle GST rate status mutation
export const useToggleGSTRateStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ rateId, isActive }: { rateId: number; isActive: boolean }) =>
      taxesApi.toggleGSTRateStatus(rateId, { is_active: isActive }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taxKeys.gstRates() });
      toast.success('GST rate status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update GST rate status');
    },
  });
};
