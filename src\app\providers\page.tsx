'use client';

import React, { useState, useMemo } from 'react';
import {
  UserPlusIcon,
  EyeIcon,
  PencilIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { useProviders, useUpdateProviderVerification, useToggleProviderAvailability } from '@/hooks/useProviders';
import { formatDateTime, formatRelativeTime, formatRating } from '@/lib/utils';
import { Provider } from '@/types/api';
import ProviderForm from '@/components/forms/ProviderForm';
import ProviderDetailsModal from '@/components/forms/ProviderDetailsModal';

export default function ProvidersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [availabilityFilter, setAvailabilityFilter] = useState('');
  const [page, setPage] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Prepare query parameters
  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      ordering: '-created_at',
      limit: 20,
      offset: (page - 1) * 20,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (statusFilter) {
      params.verification_status = statusFilter;
    }

    if (availabilityFilter) {
      params.is_available = availabilityFilter === 'available';
    }

    return params;
  }, [searchTerm, statusFilter, availabilityFilter, page]);

  // Use React Query hooks
  const { data: providersResponse, isLoading, error } = useProviders(queryParams);
  const updateVerificationMutation = useUpdateProviderVerification();
  const toggleAvailabilityMutation = useToggleProviderAvailability();

  const providers = providersResponse?.results || [];
  const totalItems = providersResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1); // Reset to first page
  };

  // Handle verification update
  const handleVerificationUpdate = (providerId: number, status: string) => {
    updateVerificationMutation.mutate({
      providerId,
      data: { verification_status: status }
    });
  };

  // Handle availability toggle
  const handleAvailabilityToggle = (providerId: number, isAvailable: boolean) => {
    toggleAvailabilityMutation.mutate({
      providerId,
      data: { is_available: !isAvailable }
    });
  };

  // Handle provider actions
  const handleViewProvider = (provider: Provider) => {
    setSelectedProvider(provider);
    setShowDetailsModal(true);
  };

  const handleEditProvider = (provider: Provider) => {
    setEditingProvider(provider);
  };

  const handleVerifyProvider = (provider: Provider, status: 'verified' | 'rejected') => {
    handleVerificationUpdate(provider.id, status);
  };

  const handleCloseModals = () => {
    setShowCreateModal(false);
    setEditingProvider(null);
    setSelectedProvider(null);
    setShowDetailsModal(false);
  };

  // Define table columns
  const columns: Column<Provider>[] = [
    {
      key: 'user_name',
      label: 'Provider',
      sortable: true,
      render: (_, provider) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {provider.profile_picture ? (
              <img
                src={provider.profile_picture}
                alt={provider.user_name}
                className="w-8 h-8 rounded-full"
              />
            ) : (
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <UserIcon className="w-4 h-4 text-gray-500" />
              </div>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900">{provider.user_name}</div>
            <div className="text-sm text-gray-500">
              {provider.business_name || 'No business name'}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'user_email',
      label: 'Contact',
      render: (_, provider) => (
        <div className="space-y-1">
          {provider.user_email && (
            <div className="flex items-center text-sm text-gray-900">
              <EnvelopeIcon className="w-4 h-4 mr-2 text-gray-400" />
              {provider.user_email}
            </div>
          )}
          {provider.user_mobile && (
            <div className="flex items-center text-sm text-gray-500">
              <PhoneIcon className="w-4 h-4 mr-2 text-gray-400" />
              {provider.user_mobile}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'verification_status',
      label: 'Verification',
      sortable: true,
      render: (value) => (
        <Badge
          variant={
            value === 'verified' ? 'success' :
            value === 'rejected' ? 'danger' :
            'warning'
          }
        >
          {value}
        </Badge>
      ),
    },
    {
      key: 'rating',
      label: 'Rating',
      render: (value, provider) => (
        <div className="flex items-center">
          <StarIcon className="w-4 h-4 text-yellow-500 mr-1" />
          <span className="font-medium">{formatRating(value)}</span>
          <span className="text-sm text-gray-500 ml-1">
            ({provider.total_reviews || 0})
          </span>
        </div>
      ),
    },
    {
      key: 'total_orders_completed',
      label: 'Orders',
      render: (value) => (
        <div className="text-sm">
          <div className="font-medium">{value || 0}</div>
          <div className="text-gray-500">completed</div>
        </div>
      ),
    },
    {
      key: 'is_available',
      label: 'Status',
      render: (value) => (
        <Badge variant={value ? 'success' : 'secondary'}>
          {value ? 'Available' : 'Unavailable'}
        </Badge>
      ),
    },
  ];

  // Define row actions
  const rowActions = (provider: Provider) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="Edit Provider">
        <PencilIcon className="w-4 h-4" />
      </Button>

      {provider.verification_status === 'pending' && (
        <>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleVerificationUpdate(provider.id, 'verified')}
            title="Verify Provider"
          >
            <CheckCircleIcon className="w-4 h-4 text-green-500" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleVerificationUpdate(provider.id, 'rejected')}
            title="Reject Provider"
          >
            <XCircleIcon className="w-4 h-4 text-red-500" />
          </Button>
        </>
      )}

      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleAvailabilityToggle(provider.id, provider.is_available)}
        title={provider.is_available ? 'Disable Provider' : 'Enable Provider'}
      >
        {provider.is_available ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Service Providers</h1>
          <Button>
            <UserPlusIcon className="w-4 h-4 mr-2" />
            Add Provider
          </Button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Verification Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="verified">Verified</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Availability
              </label>
              <select
                value={availabilityFilter}
                onChange={(e) => {
                  setAvailabilityFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All</option>
                <option value="available">Available</option>
                <option value="unavailable">Unavailable</option>
              </select>
            </div>
          </div>
        </div>

        {/* Providers Table */}
        <DataTable
          data={providers}
          columns={columns}
          loading={isLoading}
          error={error ? (error as any).message : undefined}
          searchable
          searchPlaceholder="Search providers by name, email, or business..."
          onSearch={handleSearch}
          sortable
          pagination={{
            page,
            totalPages,
            totalItems,
            itemsPerPage: 20,
            onPageChange: setPage,
          }}
          actions={rowActions}
          emptyState={{
            title: 'No providers found',
            description: 'No providers match your current filters.',
            action: (
              <Button onClick={() => {
                setSearchTerm('');
                setStatusFilter('');
                setAvailabilityFilter('');
                setPage(1);
              }}>
                Clear Filters
              </Button>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
}
