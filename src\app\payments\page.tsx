'use client';

import React, { useState, useMemo } from 'react';
import {
  CreditCardIcon,
  EyeIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BanknotesIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { formatCurrency, formatDateTime, formatRelativeTime } from '@/lib/utils';
import ApiDebugger from '@/components/debug/ApiDebugger';

// Mock Transaction type for now
interface Transaction {
  id: number;
  transaction_id: string;
  order_number: string;
  customer_name: string;
  amount: number;
  payment_method: string;
  status: string;
  gateway_response: string;
  created_at: string;
  updated_at: string;
}

// Mock data for now - will be replaced with real API calls
const mockTransactions: Transaction[] = [
  {
    id: 1,
    transaction_id: 'TXN_001',
    order_number: 'ORD_001',
    customer_name: '<PERSON>',
    amount: 1500.00,
    payment_method: 'razorpay',
    status: 'completed',
    gateway_response: 'Success',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 2,
    transaction_id: 'TXN_002',
    order_number: 'ORD_002',
    customer_name: 'Jane Smith',
    amount: 2500.00,
    payment_method: 'cod',
    status: 'pending',
    gateway_response: 'Pending',
    created_at: new Date(Date.now() - 86400000).toISOString(),
    updated_at: new Date(Date.now() - 86400000).toISOString(),
  },
];

export default function PaymentsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [methodFilter, setMethodFilter] = useState('');
  const [dateRangeFilter, setDateRangeFilter] = useState('');
  const [page, setPage] = useState(1);
  const [activeTab, setActiveTab] = useState<'transactions' | 'config'>('transactions');

  // Mock data for now
  const transactions = mockTransactions;
  const totalItems = transactions.length;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle refund
  const handleRefund = (transaction: Transaction) => {
    if (confirm(`Are you sure you want to refund ${formatCurrency(transaction.amount)}?`)) {
      // Implement refund logic
      console.log('Refunding transaction:', transaction.transaction_id);
    }
  };

  // Define table columns
  const columns: Column<Transaction>[] = [
    {
      key: 'transaction_id',
      label: 'Transaction',
      sortable: true,
      render: (_, transaction) => (
        <div>
          <div className="font-medium text-gray-900">{transaction.transaction_id}</div>
          <div className="text-sm text-gray-500">Order: {transaction.order_number}</div>
        </div>
      ),
    },
    {
      key: 'customer_name',
      label: 'Customer',
      render: (value) => (
        <div className="font-medium text-gray-900">{value}</div>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">
          {formatCurrency(value)}
        </div>
      ),
    },
    {
      key: 'payment_method',
      label: 'Method',
      render: (value) => (
        <div className="flex items-center space-x-2">
          <CreditCardIcon className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-900 capitalize">{value}</span>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge
          variant={
            value === 'completed' ? 'success' :
            value === 'failed' ? 'danger' :
            value === 'refunded' ? 'warning' :
            'secondary'
          }
        >
          {value}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Date',
      sortable: true,
      render: (value) => (
        <div>
          <div className="text-sm text-gray-900">{formatDateTime(value)}</div>
          <div className="text-xs text-gray-500">{formatRelativeTime(value)}</div>
        </div>
      ),
    },
  ];

  // Define row actions
  const rowActions = (transaction: Transaction) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>

      {transaction.status === 'completed' && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleRefund(transaction)}
          title="Process Refund"
        >
          <ArrowPathIcon className="w-4 h-4 text-orange-500" />
        </Button>
      )}

      {transaction.status === 'failed' && (
        <Button
          variant="ghost"
          size="sm"
          title="Retry Payment"
        >
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        </Button>
      )}
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <CogIcon className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('transactions')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'transactions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Transactions
            </button>
            <button
              onClick={() => setActiveTab('config')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'config'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Configuration
            </button>
          </nav>
        </div>



        {activeTab === 'transactions' && (
          <>
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Payment Status
                  </label>
                  <select
                    value={statusFilter}
                    onChange={(e) => {
                      setStatusFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Payment Method
                  </label>
                  <select
                    value={methodFilter}
                    onChange={(e) => {
                      setMethodFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Methods</option>
                    <option value="razorpay">Razorpay</option>
                    <option value="cod">Cash on Delivery</option>
                    <option value="wallet">Wallet</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Range
                  </label>
                  <select
                    value={dateRangeFilter}
                    onChange={(e) => {
                      setDateRangeFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">Last 7 Days</option>
                    <option value="month">Last 30 Days</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Transactions Table */}
            <DataTable
              data={transactions}
              columns={columns}
              loading={false}
              searchable
              searchPlaceholder="Search transactions by ID, order, or customer..."
              onSearch={handleSearch}
              sortable
              pagination={{
                page,
                totalPages,
                totalItems,
                itemsPerPage: 20,
                onPageChange: setPage,
              }}
              actions={rowActions}
              emptyState={{
                title: 'No transactions found',
                description: 'No transactions match your current filters.',
                action: (
                  <Button onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('');
                    setMethodFilter('');
                    setDateRangeFilter('');
                    setPage(1);
                  }}>
                    Clear Filters
                  </Button>
                ),
              }}
            />
          </>
        )}

        {activeTab === 'config' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Configuration</h3>
            <div className="space-y-6">
              {/* Razorpay Configuration */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Razorpay Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Test Key ID
                    </label>
                    <input
                      type="text"
                      placeholder="rzp_test_..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Test Key Secret
                    </label>
                    <input
                      type="password"
                      placeholder="••••••••"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* COD Configuration */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Cash on Delivery Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      COD Charge (%)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      placeholder="2.5"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Minimum Order Amount
                    </label>
                    <input
                      type="number"
                      placeholder="500"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>
                  Save Configuration
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
