'use client';

import React from 'react';
import { XMarkIcon, UserIcon, StarIcon, CheckCircleIcon, XCircleIcon, MapPinIcon, BriefcaseIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { formatDateTime, formatRating } from '@/lib/utils';
import { Provider } from '@/types/api';

interface ProviderDetailsModalProps {
  provider: Provider | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (provider: Provider) => void;
  onVerify?: (provider: Provider, status: 'verified' | 'rejected') => void;
  onToggleAvailability?: (provider: Provider) => void;
}

export default function ProviderDetailsModal({
  provider,
  isOpen,
  onClose,
  onEdit,
  onVerify,
  onToggleAvailability
}: ProviderDetailsModalProps) {
  if (!isOpen || !provider) return null;

  const getVerificationColor = (status: string) => {
    switch (status) {
      case 'verified': return 'success';
      case 'rejected': return 'danger';
      default: return 'warning';
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                {provider.profile_picture ? (
                  <img
                    src={provider.profile_picture}
                    alt={provider.user_name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <UserIcon className="w-6 h-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {provider.user_name}
                </h2>
                <p className="text-sm text-gray-500">
                  {provider.business_name || 'Individual Provider'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* Status and Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Badge variant={getVerificationColor(provider.verification_status)}>
                  {provider.verification_status.toUpperCase()}
                </Badge>
                <Badge variant={provider.is_available ? 'success' : 'secondary'}>
                  {provider.is_available ? 'Available' : 'Unavailable'}
                </Badge>
                {provider.accepts_new_orders && (
                  <Badge variant="info">Accepting Orders</Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                {onEdit && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(provider)}
                  >
                    Edit Provider
                  </Button>
                )}
                {onVerify && provider.verification_status === 'pending' && (
                  <>
                    <Button
                      size="sm"
                      onClick={() => onVerify(provider, 'verified')}
                    >
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      Verify
                    </Button>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={() => onVerify(provider, 'rejected')}
                    >
                      <XCircleIcon className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                  </>
                )}
                {onToggleAvailability && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onToggleAvailability(provider)}
                  >
                    {provider.is_available ? 'Disable' : 'Enable'}
                  </Button>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Contact Information</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Email:</span>
                    <span className="ml-2 text-sm text-gray-900">{provider.user_email || 'Not provided'}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Mobile:</span>
                    <span className="ml-2 text-sm text-gray-900">{provider.user_mobile || 'Not provided'}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Joined:</span>
                    <span className="ml-2 text-sm text-gray-900">{formatDateTime(provider.created_at)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Performance</h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <StarIcon className="w-4 h-4 text-yellow-500 mr-2" />
                    <span className="text-sm font-medium text-gray-700">Rating:</span>
                    <span className="ml-2 text-sm text-gray-900">
                      {formatRating(provider.rating)} ({provider.total_reviews || 0} reviews)
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Orders Completed:</span>
                    <span className="ml-2 text-sm text-gray-900">{provider.total_orders_completed || 0}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">Success Rate:</span>
                    <span className="ml-2 text-sm text-gray-900">
                      {provider.success_rate ? `${provider.success_rate}%` : 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Business Information */}
            {provider.business_name && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <BriefcaseIcon className="w-5 h-5 mr-2" />
                  Business Information
                </h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Business Name:</span>
                    <span className="ml-2 text-sm text-gray-900">{provider.business_name}</span>
                  </div>
                  {provider.business_description && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Description:</span>
                      <p className="mt-1 text-sm text-gray-900">{provider.business_description}</p>
                    </div>
                  )}
                  {provider.business_address && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Address:</span>
                      <p className="mt-1 text-sm text-gray-900">{provider.business_address}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Service Areas */}
            {provider.service_areas && provider.service_areas.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <MapPinIcon className="w-5 h-5 mr-2" />
                  Service Areas
                </h3>
                <div className="flex flex-wrap gap-2">
                  {provider.service_areas.map((area, index) => (
                    <Badge key={index} variant="secondary">
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Services Offered */}
            {provider.services_offered && provider.services_offered.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Services Offered</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {provider.services_offered.map((serviceId, index) => (
                    <div key={index} className="text-sm text-gray-700 bg-white px-3 py-2 rounded border">
                      Service ID: {serviceId}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Documents */}
            {provider.documents && provider.documents.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Documents</h3>
                <div className="space-y-2">
                  {provider.documents.map((doc, index) => (
                    <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{doc.document_type}</div>
                        <div className="text-xs text-gray-500">
                          Status: {doc.verification_status} | Uploaded: {formatDateTime(doc.uploaded_at)}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Activity */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Recent Activity</h3>
              <div className="space-y-2">
                <div className="text-sm text-gray-600">
                  Last active: {provider.last_active ? formatDateTime(provider.last_active) : 'Never'}
                </div>
                <div className="text-sm text-gray-600">
                  Profile updated: {formatDateTime(provider.updated_at)}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
