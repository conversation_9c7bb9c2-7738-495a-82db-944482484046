'use client';

import React from 'react';
import { XMarkIcon, UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, CalendarIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { formatDateTime } from '@/lib/utils';
import { User } from '@/types/api';

interface CustomerDetailsModalProps {
  customer: User | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (customer: User) => void;
  onToggleStatus?: (customer: User) => void;
  onToggleVerification?: (customer: User) => void;
}

export default function CustomerDetailsModal({
  customer,
  isOpen,
  onClose,
  onEdit,
  onToggleStatus,
  onToggleVerification
}: CustomerDetailsModalProps) {
  if (!isOpen || !customer) return null;

  const getGenderDisplay = (gender?: string) => {
    switch (gender) {
      case 'male': return 'Male';
      case 'female': return 'Female';
      case 'other': return 'Other';
      case 'prefer_not_to_say': return 'Prefer not to say';
      default: return 'Not specified';
    }
  };

  const getAddressTypeDisplay = (type: string) => {
    switch (type) {
      case 'home': return 'Home';
      case 'work': return 'Work';
      case 'other': return 'Other';
      default: return type;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Customer Details
                </h2>
                <p className="text-sm text-gray-500">
                  ID: {customer.id}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* Status and Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Badge variant={customer.is_active ? 'success' : 'secondary'}>
                  {customer.is_active ? 'Active' : 'Inactive'}
                </Badge>
                <Badge variant={customer.is_verified ? 'success' : 'warning'}>
                  {customer.is_verified ? 'Verified' : 'Unverified'}
                </Badge>
                {customer.is_locked && (
                  <Badge variant="danger">Locked</Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                {onEdit && (
                  <Button
                    size="sm"
                    onClick={() => onEdit(customer)}
                  >
                    Edit Customer
                  </Button>
                )}
                {onToggleStatus && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onToggleStatus(customer)}
                  >
                    {customer.is_active ? (
                      <>
                        <XCircleIcon className="w-4 h-4 mr-2" />
                        Deactivate
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="w-4 h-4 mr-2" />
                        Activate
                      </>
                    )}
                  </Button>
                )}
                {onToggleVerification && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onToggleVerification(customer)}
                  >
                    {customer.is_verified ? 'Unverify' : 'Verify'}
                  </Button>
                )}
              </div>
            </div>

            {/* Profile Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <UserIcon className="w-5 h-5 mr-2" />
                  Profile Information
                </h3>
                
                <div className="space-y-4">
                  {/* Profile Picture */}
                  <div className="flex items-center space-x-4">
                    {customer.profile_picture ? (
                      <img
                        src={customer.profile_picture}
                        alt={customer.name}
                        className="w-16 h-16 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                        <UserIcon className="w-8 h-8 text-gray-500" />
                      </div>
                    )}
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{customer.name}</h4>
                      <p className="text-sm text-gray-500">Customer ID: {customer.id}</p>
                    </div>
                  </div>

                  {/* Basic Details */}
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Gender:</span>
                      <span className="ml-2 text-sm text-gray-900">{getGenderDisplay(customer.gender)}</span>
                    </div>
                    
                    {customer.date_of_birth && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Date of Birth:</span>
                        <span className="ml-2 text-sm text-gray-900">
                          {new Date(customer.date_of_birth).toLocaleDateString()}
                        </span>
                      </div>
                    )}

                    <div>
                      <span className="text-sm font-medium text-gray-700">Joined:</span>
                      <span className="ml-2 text-sm text-gray-900">{formatDateTime(customer.date_joined)}</span>
                    </div>

                    {customer.last_login && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Last Login:</span>
                        <span className="ml-2 text-sm text-gray-900">{formatDateTime(customer.last_login)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                
                <div className="space-y-3">
                  {customer.email && (
                    <div className="flex items-center">
                      <EnvelopeIcon className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <span className="text-sm font-medium text-gray-700">Email:</span>
                        <span className="ml-2 text-sm text-gray-900">{customer.email}</span>
                      </div>
                    </div>
                  )}

                  {customer.mobile_number && (
                    <div className="flex items-center">
                      <PhoneIcon className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <span className="text-sm font-medium text-gray-700">Mobile:</span>
                        <span className="ml-2 text-sm text-gray-900">{customer.mobile_number}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Addresses */}
            {customer.addresses && customer.addresses.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <MapPinIcon className="w-5 h-5 mr-2" />
                  Addresses
                </h3>
                
                <div className="space-y-4">
                  {customer.addresses.map((address, index) => (
                    <div key={index} className="bg-white p-4 rounded border">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant="info">
                            {getAddressTypeDisplay(address.address_type)}
                          </Badge>
                          {address.is_default && (
                            <Badge variant="success">Default</Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-900">
                        <p>{address.street}</p>
                        <p>{address.city}, {address.state} {address.zip_code}</p>
                        {address.landmark && (
                          <p className="text-gray-600">Landmark: {address.landmark}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Account Statistics */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Account Statistics</h3>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">0</div>
                  <div className="text-sm text-gray-600">Total Orders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">₹0</div>
                  <div className="text-sm text-gray-600">Total Spent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">0</div>
                  <div className="text-sm text-gray-600">Pending Orders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">0</div>
                  <div className="text-sm text-gray-600">Reviews</div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
