'use client';

import React, { useState, useMemo } from 'react';
import {
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  FolderIcon,
  FolderOpenIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import {
  useCategories,
  useCategoryTree,
  useDeleteCategory,
  useToggleCategoryStatus
} from '@/hooks/useCategories';
import { formatDateTime } from '@/lib/utils';
import { Category } from '@/types/api';
import CategoryForm from '@/components/forms/CategoryForm';
import ApiDebugger from '@/components/debug/ApiDebugger';

export default function CategoriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [parentFilter, setParentFilter] = useState('');
  const [page, setPage] = useState(1);
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('table');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  // Prepare query parameters
  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      ordering: 'name',
      limit: 20,
      offset: (page - 1) * 20,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (statusFilter) {
      params.is_active = statusFilter === 'active';
    }

    if (parentFilter) {
      if (parentFilter === 'root') {
        params.parent__isnull = true;
      } else {
        params.parent = parentFilter;
      }
    }

    return params;
  }, [searchTerm, statusFilter, parentFilter, page]);

  // Use React Query hooks
  const { data: categoriesResponse, isLoading, error } = useCategories(queryParams);
  const { data: categoryTree } = useCategoryTree();
  const deleteCategory = useDeleteCategory();
  const toggleStatus = useToggleCategoryStatus();

  const categories = categoriesResponse?.results || [];
  const totalItems = categoriesResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle category actions
  const handleDeleteCategory = (categoryId: number) => {
    if (confirm('Are you sure you want to delete this category?')) {
      deleteCategory.mutate(categoryId);
    }
  };

  const handleToggleStatus = (category: Category) => {
    toggleStatus.mutate({
      categoryId: category.id,
      isActive: !category.is_active
    });
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingCategory(null);
  };

  // Define table columns
  const columns: Column<Category>[] = [
    {
      key: 'name',
      label: 'Category',
      sortable: true,
      render: (_, category) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {category.image ? (
              <img
                src={category.image}
                alt={category.name}
                className="w-8 h-8 rounded object-cover"
              />
            ) : (
              <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                {category.parent ? (
                  <FolderIcon className="w-4 h-4 text-gray-500" />
                ) : (
                  <FolderOpenIcon className="w-4 h-4 text-gray-500" />
                )}
              </div>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900">{category.name}</div>
            <div className="text-sm text-gray-500">
              {category.parent_name ? `Under: ${category.parent_name}` : 'Root Category'}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value) => (
        <div className="text-sm text-gray-600 max-w-xs truncate">
          {value || 'No description'}
        </div>
      ),
    },
    {
      key: 'services_count',
      label: 'Services',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {value || 0} services
        </div>
      ),
    },
    {
      key: 'level',
      label: 'Level',
      render: (value) => (
        <Badge variant="info">
          Level {value || 0}
        </Badge>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value) => (
        <Badge variant={value ? 'success' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          {formatDateTime(value)}
        </div>
      ),
    },
  ];

  // Define row actions
  const rowActions = (category: Category) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleEditCategory(category)}
        title="Edit Category"
      >
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleToggleStatus(category)}
        title={category.is_active ? 'Deactivate' : 'Activate'}
      >
        {category.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleDeleteCategory(category.id)}
        title="Delete Category"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Category Management</h1>
          <div className="flex items-center space-x-3">
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                  viewMode === 'table'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Table View
              </button>
              <button
                onClick={() => setViewMode('tree')}
                className={`px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                  viewMode === 'tree'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Tree View
              </button>
            </div>
            <Button onClick={() => setShowCreateModal(true)}>
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Category
            </Button>
          </div>
        </div>



        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Parent Category
              </label>
              <select
                value={parentFilter}
                onChange={(e) => {
                  setParentFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Categories</option>
                <option value="root">Root Categories Only</option>
                {categories.filter(cat => cat.level === 0).map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Categories Table */}
        {viewMode === 'table' && (
          <DataTable
            data={categories}
            columns={columns}
            loading={isLoading}
            error={error ? (error as any).message : undefined}
            searchable
            searchPlaceholder="Search categories by name or description..."
            onSearch={handleSearch}
            sortable
            pagination={{
              page,
              totalPages,
              totalItems,
              itemsPerPage: 20,
              onPageChange: setPage,
            }}
            actions={rowActions}
            emptyState={{
              title: 'No categories found',
              description: 'No categories match your current filters.',
              action: (
                <div className="space-y-3">
                  <Button onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('');
                    setParentFilter('');
                    setPage(1);
                  }}>
                    Clear Filters
                  </Button>
                  <div className="text-sm text-gray-500">or</div>
                  <Button onClick={() => setShowCreateModal(true)}>
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Create First Category
                  </Button>
                </div>
              ),
            }}
          />
        )}

        {/* Tree View */}
        {viewMode === 'tree' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Category Tree</h3>
            {categoryTree ? (
              <div className="space-y-2">
                {/* Tree view implementation would go here */}
                <pre className="text-sm text-gray-600">
                  {JSON.stringify(categoryTree, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Loading category tree...
              </div>
            )}
          </div>
        )}

        {/* Category Form Modal */}
        <CategoryForm
          category={editingCategory}
          isOpen={showCreateModal || !!editingCategory}
          onClose={handleCloseModal}
          onSuccess={handleCloseModal}
        />
      </div>
    </DashboardLayout>
  );
}
