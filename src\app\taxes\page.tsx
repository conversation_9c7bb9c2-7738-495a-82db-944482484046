'use client';

import React, { useState, useMemo } from 'react';
import { 
  PlusIcon, 
  EyeIcon, 
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  CalculatorIcon,
  DocumentTextIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import ApiDebugger from '@/components/debug/ApiDebugger';

// Mock Tax Configuration type
interface TaxConfig {
  id: number;
  name: string;
  tax_type: 'GST' | 'VAT' | 'SERVICE_TAX';
  rate: number;
  applicable_from: string;
  applicable_to?: string;
  is_active: boolean;
  description: string;
  created_at: string;
}

// Mock data for now
const mockTaxConfigs: TaxConfig[] = [
  {
    id: 1,
    name: 'CGST',
    tax_type: 'GST',
    rate: 9.0,
    applicable_from: '2023-01-01',
    is_active: true,
    description: 'Central Goods and Services Tax',
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    name: 'SGST',
    tax_type: 'GST',
    rate: 9.0,
    applicable_from: '2023-01-01',
    is_active: true,
    description: 'State Goods and Services Tax',
    created_at: new Date().toISOString(),
  },
  {
    id: 3,
    name: 'IGST',
    tax_type: 'GST',
    rate: 18.0,
    applicable_from: '2023-01-01',
    is_active: true,
    description: 'Integrated Goods and Services Tax',
    created_at: new Date().toISOString(),
  },
];

export default function TaxesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [activeTab, setActiveTab] = useState<'config' | 'reports'>('config');

  // Mock data for now
  const taxConfigs = mockTaxConfigs;
  const totalItems = taxConfigs.length;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle tax actions
  const handleDeleteTax = (taxId: number) => {
    if (confirm('Are you sure you want to delete this tax configuration?')) {
      console.log('Deleting tax:', taxId);
    }
  };

  const handleToggleStatus = (tax: TaxConfig) => {
    console.log('Toggling tax status:', tax.id);
  };

  // Define table columns
  const columns: Column<TaxConfig>[] = [
    {
      key: 'name',
      label: 'Tax Name',
      sortable: true,
      render: (_, tax) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
              <CalculatorIcon className="w-4 h-4 text-green-600" />
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900">{tax.name}</div>
            <div className="text-sm text-gray-500">{tax.description}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'tax_type',
      label: 'Type',
      render: (value) => (
        <Badge variant="info">
          {value}
        </Badge>
      ),
    },
    {
      key: 'rate',
      label: 'Rate',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">
          {value}%
        </div>
      ),
    },
    {
      key: 'applicable_from',
      label: 'Applicable From',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value) => (
        <Badge variant={value ? 'success' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          {formatDateTime(value)}
        </div>
      ),
    },
  ];

  // Define row actions
  const rowActions = (tax: TaxConfig) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="Edit Tax">
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleToggleStatus(tax)}
        title={tax.is_active ? 'Deactivate' : 'Activate'}
      >
        {tax.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleDeleteTax(tax.id)}
        title="Delete Tax"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Tax Management</h1>
          <Button>
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Tax Configuration
          </Button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('config')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'config'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Tax Configuration
            </button>
            <button
              onClick={() => setActiveTab('reports')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reports'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Tax Reports
            </button>
          </nav>
        </div>



        {activeTab === 'config' && (
          <>
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tax Type
                  </label>
                  <select
                    value={typeFilter}
                    onChange={(e) => {
                      setTypeFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Types</option>
                    <option value="GST">GST</option>
                    <option value="VAT">VAT</option>
                    <option value="SERVICE_TAX">Service Tax</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={statusFilter}
                    onChange={(e) => {
                      setStatusFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Tax Configuration Table */}
            <DataTable
              data={taxConfigs}
              columns={columns}
              loading={false}
              searchable
              searchPlaceholder="Search tax configurations..."
              onSearch={handleSearch}
              sortable
              pagination={{
                page,
                totalPages,
                totalItems,
                itemsPerPage: 20,
                onPageChange: setPage,
              }}
              actions={rowActions}
              emptyState={{
                title: 'No tax configurations found',
                description: 'No tax configurations match your current filters.',
                action: (
                  <div className="space-y-3">
                    <Button onClick={() => {
                      setSearchTerm('');
                      setTypeFilter('');
                      setStatusFilter('');
                      setPage(1);
                    }}>
                      Clear Filters
                    </Button>
                    <div className="text-sm text-gray-500">or</div>
                    <Button>
                      <PlusIcon className="w-4 h-4 mr-2" />
                      Create First Tax Configuration
                    </Button>
                  </div>
                ),
              }}
            />
          </>
        )}

        {activeTab === 'reports' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tax Reports</h3>
            <div className="text-center py-12 text-gray-500">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>Tax reports and analytics will be implemented here</p>
              <p className="text-sm">This will show tax collection summaries, compliance reports, etc.</p>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
