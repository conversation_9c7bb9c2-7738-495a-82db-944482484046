'use client';

import React, { useState, useMemo } from 'react';
import { 
  PlusIcon, 
  EyeIcon, 
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  CalculatorIcon,
  DocumentTextIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { useTaxCategories, useGSTRates, useDeleteTaxCategory, useDeleteGSTRate, useToggleTaxCategoryStatus, useToggleGSTRateStatus } from '@/hooks/useTaxes';
import { TaxCategory, GSTRate } from '@/types/api';
import TaxCategoryForm from '@/components/forms/TaxCategoryForm';
import GSTRateForm from '@/components/forms/GSTRateForm';
import ApiDebugger from '@/components/debug/ApiDebugger';

// Mock Tax Configuration type
interface TaxConfig {
  id: number;
  name: string;
  tax_type: 'GST' | 'VAT' | 'SERVICE_TAX';
  rate: number;
  applicable_from: string;
  applicable_to?: string;
  is_active: boolean;
  description: string;
  created_at: string;
}

// Mock data for now
const mockTaxConfigs: TaxConfig[] = [
  {
    id: 1,
    name: 'CGST',
    tax_type: 'GST',
    rate: 9.0,
    applicable_from: '2023-01-01',
    is_active: true,
    description: 'Central Goods and Services Tax',
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    name: 'SGST',
    tax_type: 'GST',
    rate: 9.0,
    applicable_from: '2023-01-01',
    is_active: true,
    description: 'State Goods and Services Tax',
    created_at: new Date().toISOString(),
  },
  {
    id: 3,
    name: 'IGST',
    tax_type: 'GST',
    rate: 18.0,
    applicable_from: '2023-01-01',
    is_active: true,
    description: 'Integrated Goods and Services Tax',
    created_at: new Date().toISOString(),
  },
];

export default function TaxesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [activeTab, setActiveTab] = useState<'categories' | 'rates' | 'reports'>('categories');
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showRateModal, setShowRateModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<TaxCategory | null>(null);
  const [editingRate, setEditingRate] = useState<GSTRate | null>(null);

  // API calls
  const deleteTaxCategoryMutation = useDeleteTaxCategory();
  const deleteGSTRateMutation = useDeleteGSTRate();
  const toggleCategoryStatusMutation = useToggleTaxCategoryStatus();
  const toggleRateStatusMutation = useToggleGSTRateStatus();

  const { data: categoriesResponse, isLoading: categoriesLoading } = useTaxCategories({
    page,
    search: searchTerm,
    is_active: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
  });

  const { data: ratesResponse, isLoading: ratesLoading } = useGSTRates({
    page,
    search: searchTerm,
    is_active: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
  });

  const categories = categoriesResponse?.results || [];
  const rates = ratesResponse?.results || [];
  const totalItems = activeTab === 'categories' ? categoriesResponse?.count || 0 : ratesResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);
  const isLoading = activeTab === 'categories' ? categoriesLoading : ratesLoading;

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1);
  };

  // Handle tax actions
  const handleEditCategory = (category: TaxCategory) => {
    setEditingCategory(category);
  };

  const handleEditRate = (rate: GSTRate) => {
    setEditingRate(rate);
  };

  const handleDeleteCategory = (categoryId: number) => {
    if (confirm('Are you sure you want to delete this tax category?')) {
      deleteTaxCategoryMutation.mutate(categoryId);
    }
  };

  const handleDeleteRate = (rateId: number) => {
    if (confirm('Are you sure you want to delete this GST rate?')) {
      deleteGSTRateMutation.mutate(rateId);
    }
  };

  const handleToggleCategoryStatus = (category: TaxCategory) => {
    toggleCategoryStatusMutation.mutate({
      categoryId: category.id,
      isActive: !category.is_active
    });
  };

  const handleToggleRateStatus = (rate: GSTRate) => {
    toggleRateStatusMutation.mutate({
      rateId: rate.id,
      isActive: !rate.is_active
    });
  };

  const handleCloseModals = () => {
    setShowCategoryModal(false);
    setShowRateModal(false);
    setEditingCategory(null);
    setEditingRate(null);
  };

  // Define table columns for categories
  const categoryColumns: Column<TaxCategory>[] = [
    {
      key: 'name',
      label: 'Tax Name',
      sortable: true,
      render: (_, category) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
              <CalculatorIcon className="w-4 h-4 text-green-600" />
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900">{category.name}</div>
            <div className="text-sm text-gray-500">{category.description}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'gst_rate',
      label: 'GST Rate',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">
          {value}%
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value) => (
        <Badge variant={value ? 'success' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          {formatDateTime(value)}
        </div>
      ),
    },
  ];

  // Define row actions for categories
  const categoryRowActions = (category: TaxCategory) => (
    <div className="flex items-center space-x-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleEditCategory(category)}
        title="Edit Category"
      >
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleToggleCategoryStatus(category)}
        title={category.is_active ? 'Deactivate' : 'Activate'}
      >
        {category.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleDeleteCategory(category.id)}
        title="Delete Category"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  // Define GST rate columns
  const rateColumns: Column<GSTRate>[] = [
    {
      key: 'rate',
      label: 'GST Rate',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">
          {value}%
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value) => (
        <div className="text-sm text-gray-900">
          {value}
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value) => (
        <Badge variant={value ? 'success' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          {formatDateTime(value)}
        </div>
      ),
    },
  ];

  // Define row actions for rates
  const rateRowActions = (rate: GSTRate) => (
    <div className="flex items-center space-x-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleEditRate(rate)}
        title="Edit Rate"
      >
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleToggleRateStatus(rate)}
        title={rate.is_active ? 'Deactivate' : 'Activate'}
      >
        {rate.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleDeleteRate(rate.id)}
        title="Delete Rate"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Tax Management</h1>
          <div className="flex space-x-2">
            {activeTab === 'categories' && (
              <Button onClick={() => setShowCategoryModal(true)}>
                <PlusIcon className="w-4 h-4 mr-2" />
                Add Tax Category
              </Button>
            )}
            {activeTab === 'rates' && (
              <Button onClick={() => setShowRateModal(true)}>
                <PlusIcon className="w-4 h-4 mr-2" />
                Add GST Rate
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('categories')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'categories'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Tax Categories
            </button>
            <button
              onClick={() => setActiveTab('rates')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'rates'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              GST Rates
            </button>
            <button
              onClick={() => setActiveTab('reports')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reports'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Tax Reports
            </button>
          </nav>
        </div>



        {(activeTab === 'categories' || activeTab === 'rates') && (
          <>
            {/* Filters */}
            <div className="bg-white p-4 rounded-lg shadow space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">


                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={statusFilter}
                    onChange={(e) => {
                      setStatusFilter(e.target.value);
                      setPage(1);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Data Table */}
            <DataTable
              data={activeTab === 'categories' ? categories : rates}
              columns={activeTab === 'categories' ? categoryColumns : rateColumns}
              loading={isLoading}
              searchable
              searchPlaceholder={`Search ${activeTab}...`}
              onSearch={handleSearch}
              sortable
              pagination={{
                page,
                totalPages,
                totalItems,
                itemsPerPage: 20,
                onPageChange: setPage,
              }}
              actions={activeTab === 'categories' ? categoryRowActions : rateRowActions}
              emptyState={{
                title: `No ${activeTab} found`,
                description: `No ${activeTab} match your current filters.`,
                action: (
                  <div className="space-y-3">
                    <Button onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('');
                      setPage(1);
                    }}>
                      Clear Filters
                    </Button>
                    <div className="text-sm text-gray-500">or</div>
                    <Button onClick={() => activeTab === 'categories' ? setShowCategoryModal(true) : setShowRateModal(true)}>
                      <PlusIcon className="w-4 h-4 mr-2" />
                      Create First {activeTab === 'categories' ? 'Tax Category' : 'GST Rate'}
                    </Button>
                  </div>
                ),
              }}
            />
          </>
        )}

        {activeTab === 'reports' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tax Reports</h3>
            <div className="text-center py-12 text-gray-500">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>Tax reports and analytics will be implemented here</p>
              <p className="text-sm">This will show tax collection summaries, compliance reports, etc.</p>
            </div>
          </div>
        )}

        {/* Tax Category Form Modal */}
        <TaxCategoryForm
          taxCategory={editingCategory}
          isOpen={showCategoryModal || !!editingCategory}
          onClose={handleCloseModals}
          onSuccess={handleCloseModals}
        />

        {/* GST Rate Form Modal */}
        <GSTRateForm
          gstRate={editingRate}
          isOpen={showRateModal || !!editingRate}
          onClose={handleCloseModals}
          onSuccess={handleCloseModals}
        />
      </div>
    </DashboardLayout>
  );
}
