'use client';

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select } from '@/components/ui/Form';
import { useCreateOrder } from '@/hooks/useOrders';
import { useUsers } from '@/hooks/useUsers';
import { useServices } from '@/hooks/useServices';
import { createOrderSchema, CreateOrderFormData } from '@/lib/validations';

interface CreateOrderFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function CreateOrderForm({ isOpen, onClose, onSuccess }: CreateOrderFormProps) {
  const createOrderMutation = useCreateOrder();
  const { data: usersResponse } = useUsers({ user_type: 'CUSTOMER', limit: 100 });
  const { data: servicesResponse } = useServices({ is_active: true, limit: 100 });

  const customers = usersResponse?.results || [];
  const services = servicesResponse?.results || [];

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    control,
    watch,
  } = useForm<CreateOrderFormData>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      customer: 0,
      items: [{ service_id: 0, quantity: 1, special_instructions: '' }],
      delivery_address: {
        street: '',
        city: '',
        state: '',
        zip_code: '',
        landmark: '',
      },
      scheduled_date: '',
      scheduled_time_slot: '',
      payment_method: 'cod',
      coupon_code: '',
      customer_notes: '',
      admin_notes: '',
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  });

  const onSubmit = async (data: CreateOrderFormData) => {
    try {
      // Clean up empty fields
      const cleanedData = {
        ...data,
        items: data.items.filter(item => item.service_id > 0),
        coupon_code: data.coupon_code || undefined,
        customer_notes: data.customer_notes || undefined,
        admin_notes: data.admin_notes || undefined,
        delivery_address: {
          ...data.delivery_address,
          landmark: data.delivery_address.landmark || undefined,
        },
      };

      await createOrderMutation.mutateAsync(cleanedData);
      onSuccess?.();
      onClose();
      reset();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              Create New Order
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Customer Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Customer Information</h3>
              
              <FormField
                label="Customer"
                required
                error={errors.customer}
              >
                <Select
                  {...register('customer', { valueAsNumber: true })}
                  options={[
                    { value: 0, label: 'Select a customer...' },
                    ...customers.map(customer => ({
                      value: customer.id,
                      label: `${customer.name} - ${customer.mobile_number || customer.email}`
                    }))
                  ]}
                  placeholder="Choose customer"
                  error={!!errors.customer}
                />
              </FormField>
            </div>

            {/* Delivery Address */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Delivery Address</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <FormField
                    label="Street Address"
                    required
                    error={errors.delivery_address?.street}
                  >
                    <Input
                      {...register('delivery_address.street')}
                      placeholder="Enter street address"
                      error={!!errors.delivery_address?.street}
                    />
                  </FormField>
                </div>

                <FormField
                  label="City"
                  required
                  error={errors.delivery_address?.city}
                >
                  <Input
                    {...register('delivery_address.city')}
                    placeholder="Enter city"
                    error={!!errors.delivery_address?.city}
                  />
                </FormField>

                <FormField
                  label="State"
                  required
                  error={errors.delivery_address?.state}
                >
                  <Input
                    {...register('delivery_address.state')}
                    placeholder="Enter state"
                    error={!!errors.delivery_address?.state}
                  />
                </FormField>

                <FormField
                  label="ZIP Code"
                  required
                  error={errors.delivery_address?.zip_code}
                >
                  <Input
                    {...register('delivery_address.zip_code')}
                    placeholder="Enter ZIP code"
                    error={!!errors.delivery_address?.zip_code}
                  />
                </FormField>

                <FormField
                  label="Landmark (Optional)"
                  error={errors.delivery_address?.landmark}
                >
                  <Input
                    {...register('delivery_address.landmark')}
                    placeholder="Enter landmark"
                    error={!!errors.delivery_address?.landmark}
                  />
                </FormField>
              </div>
            </div>

            {/* Order Items */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Order Items</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => append({ service_id: 0, quantity: 1, special_instructions: '' })}
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add Item
                </Button>
              </div>

              {fields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-md font-medium text-gray-700">Item {index + 1}</h4>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        <TrashIcon className="w-4 h-4 text-red-500" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="md:col-span-2">
                      <FormField
                        label="Service"
                        required
                        error={errors.items?.[index]?.service_id}
                      >
                        <Select
                          {...register(`items.${index}.service_id`, { valueAsNumber: true })}
                          options={[
                            { value: 0, label: 'Select a service...' },
                            ...services.map(service => ({
                              value: service.id,
                              label: `${service.title} - ₹${service.current_price}`
                            }))
                          ]}
                          placeholder="Choose service"
                          error={!!errors.items?.[index]?.service_id}
                        />
                      </FormField>
                    </div>

                    <FormField
                      label="Quantity"
                      required
                      error={errors.items?.[index]?.quantity}
                    >
                      <Input
                        {...register(`items.${index}.quantity`, { valueAsNumber: true })}
                        type="number"
                        min="1"
                        placeholder="1"
                        error={!!errors.items?.[index]?.quantity}
                      />
                    </FormField>
                  </div>

                  <FormField
                    label="Special Instructions (Optional)"
                    error={errors.items?.[index]?.special_instructions}
                  >
                    <Textarea
                      {...register(`items.${index}.special_instructions`)}
                      placeholder="Any special instructions for this service..."
                      rows={2}
                      error={!!errors.items?.[index]?.special_instructions}
                    />
                  </FormField>
                </div>
              ))}
            </div>

            {/* Scheduling */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Scheduling</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Scheduled Date"
                  required
                  error={errors.scheduled_date}
                >
                  <Input
                    {...register('scheduled_date')}
                    type="date"
                    min={new Date().toISOString().split('T')[0]}
                    error={!!errors.scheduled_date}
                  />
                </FormField>

                <FormField
                  label="Time Slot"
                  required
                  error={errors.scheduled_time_slot}
                >
                  <Select
                    {...register('scheduled_time_slot')}
                    options={[
                      { value: '', label: 'Select time slot...' },
                      { value: '09:00-12:00', label: '9:00 AM - 12:00 PM' },
                      { value: '12:00-15:00', label: '12:00 PM - 3:00 PM' },
                      { value: '15:00-18:00', label: '3:00 PM - 6:00 PM' },
                      { value: '18:00-21:00', label: '6:00 PM - 9:00 PM' },
                    ]}
                    error={!!errors.scheduled_time_slot}
                  />
                </FormField>
              </div>
            </div>

            {/* Payment and Additional Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Payment & Additional Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Payment Method"
                  required
                  error={errors.payment_method}
                >
                  <Select
                    {...register('payment_method')}
                    options={[
                      { value: 'cod', label: 'Cash on Delivery' },
                      { value: 'razorpay', label: 'Online Payment' },
                    ]}
                    error={!!errors.payment_method}
                  />
                </FormField>

                <FormField
                  label="Coupon Code (Optional)"
                  error={errors.coupon_code}
                >
                  <Input
                    {...register('coupon_code')}
                    placeholder="Enter coupon code"
                    error={!!errors.coupon_code}
                  />
                </FormField>
              </div>

              <FormField
                label="Customer Notes (Optional)"
                error={errors.customer_notes}
              >
                <Textarea
                  {...register('customer_notes')}
                  placeholder="Any notes from the customer..."
                  rows={3}
                  error={!!errors.customer_notes}
                />
              </FormField>

              <FormField
                label="Admin Notes (Optional)"
                error={errors.admin_notes}
              >
                <Textarea
                  {...register('admin_notes')}
                  placeholder="Internal notes for this order..."
                  rows={3}
                  error={!!errors.admin_notes}
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Order'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
