import { z } from 'zod';

// Common validation schemas
export const emailSchema = z.string().email('Please enter a valid email address');
export const phoneSchema = z.string().regex(/^\+?[\d\s-()]+$/, 'Please enter a valid phone number');
export const passwordSchema = z.string().min(8, 'Password must be at least 8 characters');

// Authentication schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

export const changePasswordSchema = z.object({
  old_password: z.string().min(1, 'Current password is required'),
  new_password: passwordSchema,
  confirm_password: z.string(),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

// User schemas
export const userSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: emailSchema.optional(),
  mobile_number: phoneSchema.optional(),
  user_type: z.enum(['CUSTOMER', 'PROVIDER', 'STAFF']),
  is_verified: z.boolean(),
  is_active: z.boolean(),
});

export const userUpdateSchema = userSchema.partial();

// Service schemas
export const serviceSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  category: z.number().min(1, 'Category is required'),
  description: z.string().min(1, 'Description is required'),
  base_price: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid price'),
  discount_price: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid price').optional().or(z.literal('')),
  time_to_complete: z.string().min(1, 'Time to complete is required'),
  is_active: z.boolean(),
  requires_partial_payment: z.boolean(),
  partial_payment_type: z.enum(['percentage', 'fixed']).optional(),
  partial_payment_value: z.string().optional(),
  partial_payment_description: z.string().optional(),
});

// Category schemas
export const categorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  parent: z.number().optional(),
  is_active: z.boolean(),
});

// Order schemas
export const orderStatusUpdateSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'assigned', 'in_progress', 'completed', 'cancelled', 'refunded']),
  admin_notes: z.string().optional(),
});

export const assignProviderSchema = z.object({
  provider_id: z.number().min(1, 'Provider is required'),
  admin_notes: z.string().optional(),
});

export const cancelOrderSchema = z.object({
  reason: z.string().min(1, 'Cancellation reason is required'),
  admin_notes: z.string().optional(),
});

// Order creation schema
export const createOrderSchema = z.object({
  customer: z.number().min(1, 'Customer is required'),
  items: z.array(z.object({
    service_id: z.number().min(1, 'Service is required'),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    special_instructions: z.string().optional(),
  })).min(1, 'At least one item is required'),
  delivery_address: z.object({
    street: z.string().min(1, 'Street address is required'),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(1, 'State is required'),
    zip_code: z.string().min(1, 'ZIP code is required'),
    landmark: z.string().optional(),
  }),
  scheduled_date: z.string().min(1, 'Scheduled date is required'),
  scheduled_time_slot: z.string().min(1, 'Time slot is required'),
  payment_method: z.enum(['razorpay', 'cod']),
  coupon_code: z.string().optional(),
  customer_notes: z.string().optional(),
  admin_notes: z.string().optional(),
});

// Provider schemas
export const providerSchema = z.object({
  business_name: z.string().min(1, 'Business name is required'),
  business_description: z.string().optional(),
  business_address: z.string().optional(),
  verification_status: z.enum(['pending', 'verified', 'rejected']),
  services_offered: z.array(z.number()),
  service_areas: z.array(z.string()),
  is_available: z.boolean(),
  accepts_new_orders: z.boolean(),
});

// Coupon schemas
export const couponSchema = z.object({
  code: z.string().min(1, 'Coupon code is required').max(20, 'Code must be 20 characters or less'),
  name: z.string().min(1, 'Coupon name is required'),
  discount_type: z.enum(['percentage', 'fixed']),
  discount_value: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid discount value'),
  minimum_order_amount: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid amount').optional().or(z.literal('')),
  maximum_discount_amount: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid amount').optional().or(z.literal('')),
  usage_limit: z.number().min(1, 'Usage limit must be at least 1').optional(),
  usage_limit_per_user: z.number().min(1, 'Usage limit per user must be at least 1').optional(),
  valid_from: z.string().min(1, 'Start date is required'),
  valid_until: z.string().min(1, 'End date is required'),
  is_active: z.boolean(),
});

// Payment schemas
export const paymentConfigSchema = z.object({
  razorpay_test_key_id: z.string().optional(),
  razorpay_test_key_secret: z.string().optional(),
  razorpay_live_key_id: z.string().optional(),
  razorpay_live_key_secret: z.string().optional(),
  active_environment: z.enum(['test', 'live']),
  enable_razorpay: z.boolean(),
  enable_cod: z.boolean(),
  cod_charge_percentage: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid percentage'),
  cod_minimum_order: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid amount'),
});

// Scheduling schemas
export const timeSlotSchema = z.object({
  date: z.string().min(1, 'Date is required'),
  start_time: z.string().min(1, 'Start time is required'),
  end_time: z.string().min(1, 'End time is required'),
  max_bookings: z.number().min(1, 'Maximum bookings must be at least 1'),
  status: z.enum(['available', 'booked', 'blocked']),
  blocked_reason: z.string().optional(),
  notes: z.string().optional(),
});

// Tax schemas
export const taxCategorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  gst_rate: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid GST rate'),
  is_active: z.boolean(),
});

export const gstRateSchema = z.object({
  rate: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Please enter a valid rate'),
  description: z.string().min(1, 'Description is required'),
  is_active: z.boolean(),
});

// Export types
export type LoginFormData = z.infer<typeof loginSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type UserFormData = z.infer<typeof userSchema>;
export type UserUpdateFormData = z.infer<typeof userUpdateSchema>;
export type ServiceFormData = z.infer<typeof serviceSchema>;
export type CategoryFormData = z.infer<typeof categorySchema>;
export type OrderStatusUpdateFormData = z.infer<typeof orderStatusUpdateSchema>;
export type AssignProviderFormData = z.infer<typeof assignProviderSchema>;
export type CancelOrderFormData = z.infer<typeof cancelOrderSchema>;
export type CreateOrderFormData = z.infer<typeof createOrderSchema>;
export type ProviderFormData = z.infer<typeof providerSchema>;
export type CouponFormData = z.infer<typeof couponSchema>;
export type PaymentConfigFormData = z.infer<typeof paymentConfigSchema>;
export type TimeSlotFormData = z.infer<typeof timeSlotSchema>;
export type TaxCategoryFormData = z.infer<typeof taxCategorySchema>;
export type GSTRateFormData = z.infer<typeof gstRateSchema>;
