'use client';

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, UserIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select, Checkbox, FileUpload } from '@/components/ui/Form';
import { useCreateUser, useUpdateUser } from '@/hooks/useUsers';
import { customerSchema, CustomerFormData } from '@/lib/validations';
import { User } from '@/types/api';

interface CustomerFormProps {
  customer?: User;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function CustomerForm({ customer, isOpen, onClose, onSuccess }: CustomerFormProps) {
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);

  const isEditing = !!customer;
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    control,
    watch,
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: '',
      email: '',
      mobile_number: '',
      date_of_birth: '',
      gender: '',
      addresses: [{ 
        street: '', 
        city: '', 
        state: '', 
        zip_code: '', 
        landmark: '', 
        address_type: 'home',
        is_default: true 
      }],
      is_active: true,
      is_verified: false,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'addresses',
  });

  // Reset form when customer changes
  useEffect(() => {
    if (customer) {
      reset({
        name: customer.name,
        email: customer.email || '',
        mobile_number: customer.mobile_number || '',
        date_of_birth: customer.date_of_birth || '',
        gender: customer.gender || '',
        addresses: customer.addresses?.length ? customer.addresses : [{ 
          street: '', 
          city: '', 
          state: '', 
          zip_code: '', 
          landmark: '', 
          address_type: 'home',
          is_default: true 
        }],
        is_active: customer.is_active,
        is_verified: customer.is_verified,
      });
      setProfilePicturePreview(customer.profile_picture || null);
    } else {
      reset();
      setProfilePicturePreview(null);
      setProfilePictureFile(null);
    }
  }, [customer, reset]);

  const handleProfilePictureSelect = (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0];
      setProfilePictureFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: CustomerFormData) => {
    try {
      const formData = new FormData();
      
      // Add form fields
      formData.append('name', data.name);
      formData.append('user_type', 'CUSTOMER');
      if (data.email) formData.append('email', data.email);
      if (data.mobile_number) formData.append('mobile_number', data.mobile_number);
      if (data.date_of_birth) formData.append('date_of_birth', data.date_of_birth);
      if (data.gender) formData.append('gender', data.gender);
      formData.append('is_active', data.is_active.toString());
      formData.append('is_verified', data.is_verified.toString());

      // Add addresses
      if (data.addresses && data.addresses.length > 0) {
        formData.append('addresses', JSON.stringify(data.addresses.filter(addr => addr.street.trim())));
      }

      // Add profile picture if selected
      if (profilePictureFile) {
        formData.append('profile_picture', profilePictureFile);
      }

      if (isEditing && customer) {
        await updateUserMutation.mutateAsync({
          userId: customer.id,
          data: formData,
        });
      } else {
        await createUserMutation.mutateAsync(formData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    reset();
    setProfilePicturePreview(null);
    setProfilePictureFile(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditing ? 'Edit Customer' : 'Add New Customer'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Profile Picture */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Profile Picture</h3>
              
              {profilePicturePreview ? (
                <div className="relative w-24 h-24">
                  <img
                    src={profilePicturePreview}
                    alt="Profile preview"
                    className="w-24 h-24 object-cover rounded-full"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setProfilePicturePreview(null);
                      setProfilePictureFile(null);
                    }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <FileUpload
                  accept="image/*"
                  maxSize={5}
                  onFileSelect={handleProfilePictureSelect}
                />
              )}
            </div>

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <FormField
                label="Full Name"
                required
                error={errors.name}
              >
                <Input
                  {...register('name')}
                  placeholder="Enter customer's full name"
                  error={!!errors.name}
                />
              </FormField>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Email Address"
                  error={errors.email}
                >
                  <Input
                    {...register('email')}
                    type="email"
                    placeholder="<EMAIL>"
                    error={!!errors.email}
                  />
                </FormField>

                <FormField
                  label="Mobile Number"
                  error={errors.mobile_number}
                >
                  <Input
                    {...register('mobile_number')}
                    type="tel"
                    placeholder="+91 9876543210"
                    error={!!errors.mobile_number}
                  />
                </FormField>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Date of Birth"
                  error={errors.date_of_birth}
                >
                  <Input
                    {...register('date_of_birth')}
                    type="date"
                    error={!!errors.date_of_birth}
                  />
                </FormField>

                <FormField
                  label="Gender"
                  error={errors.gender}
                >
                  <Select
                    {...register('gender')}
                    options={[
                      { value: '', label: 'Select gender...' },
                      { value: 'male', label: 'Male' },
                      { value: 'female', label: 'Female' },
                      { value: 'other', label: 'Other' },
                      { value: 'prefer_not_to_say', label: 'Prefer not to say' },
                    ]}
                    error={!!errors.gender}
                  />
                </FormField>
              </div>
            </div>

            {/* Addresses */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Addresses</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => append({ 
                    street: '', 
                    city: '', 
                    state: '', 
                    zip_code: '', 
                    landmark: '', 
                    address_type: 'home',
                    is_default: false 
                  })}
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add Address
                </Button>
              </div>

              {fields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-md font-medium text-gray-700">Address {index + 1}</h4>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        <TrashIcon className="w-4 h-4 text-red-500" />
                      </Button>
                    )}
                  </div>

                  <FormField
                    label="Street Address"
                    required
                    error={errors.addresses?.[index]?.street}
                  >
                    <Textarea
                      {...register(`addresses.${index}.street`)}
                      placeholder="Enter complete street address"
                      rows={2}
                      error={!!errors.addresses?.[index]?.street}
                    />
                  </FormField>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      label="City"
                      required
                      error={errors.addresses?.[index]?.city}
                    >
                      <Input
                        {...register(`addresses.${index}.city`)}
                        placeholder="City"
                        error={!!errors.addresses?.[index]?.city}
                      />
                    </FormField>

                    <FormField
                      label="State"
                      required
                      error={errors.addresses?.[index]?.state}
                    >
                      <Input
                        {...register(`addresses.${index}.state`)}
                        placeholder="State"
                        error={!!errors.addresses?.[index]?.state}
                      />
                    </FormField>

                    <FormField
                      label="ZIP Code"
                      required
                      error={errors.addresses?.[index]?.zip_code}
                    >
                      <Input
                        {...register(`addresses.${index}.zip_code`)}
                        placeholder="ZIP Code"
                        error={!!errors.addresses?.[index]?.zip_code}
                      />
                    </FormField>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      label="Landmark (Optional)"
                      error={errors.addresses?.[index]?.landmark}
                    >
                      <Input
                        {...register(`addresses.${index}.landmark`)}
                        placeholder="Nearby landmark"
                        error={!!errors.addresses?.[index]?.landmark}
                      />
                    </FormField>

                    <FormField
                      label="Address Type"
                      error={errors.addresses?.[index]?.address_type}
                    >
                      <Select
                        {...register(`addresses.${index}.address_type`)}
                        options={[
                          { value: 'home', label: 'Home' },
                          { value: 'work', label: 'Work' },
                          { value: 'other', label: 'Other' },
                        ]}
                        error={!!errors.addresses?.[index]?.address_type}
                      />
                    </FormField>
                  </div>

                  <FormField error={errors.addresses?.[index]?.is_default}>
                    <Checkbox
                      {...register(`addresses.${index}.is_default`)}
                      label="Set as default address"
                    />
                  </FormField>
                </div>
              ))}
            </div>

            {/* Status Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Account Settings</h3>
              
              <div className="flex space-x-6">
                <FormField error={errors.is_active}>
                  <Checkbox
                    {...register('is_active')}
                    label="Active Account"
                    description="Customer can access their account and place orders"
                  />
                </FormField>

                <FormField error={errors.is_verified}>
                  <Checkbox
                    {...register('is_verified')}
                    label="Verified Customer"
                    description="Customer has completed verification process"
                  />
                </FormField>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Customer' : 'Add Customer')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
