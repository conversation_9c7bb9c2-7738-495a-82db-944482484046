import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { usersApi } from '@/lib/api';
import { User, PaginatedResponse } from '@/types/api';

// Query keys
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...userKeys.lists(), params] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (userId: number) => [...userKeys.details(), userId] as const,
};

// Get users list with filters
export const useUsers = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: userKeys.list(params),
    queryFn: () => usersApi.getUsers(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single user details
export const useUser = (userId: number) => {
  return useQuery({
    queryKey: userKeys.detail(userId),
    queryFn: () => usersApi.getUserDetail(userId),
    enabled: !!userId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Create user mutation
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData | any) => usersApi.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      toast.success('User created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create user');
    },
  });
};

// Update user mutation
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, data }: { userId: number; data: FormData | Partial<User> }) =>
      usersApi.updateUser(userId, data),
    onSuccess: (_, variables) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      // Invalidate specific user detail
      queryClient.invalidateQueries({ queryKey: userKeys.detail(variables.userId) });
      toast.success('User updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update user');
    },
  });
};

// Toggle user lock mutation
export const useToggleUserLock = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, data }: { userId: number; data: { is_locked: boolean; lockout_duration?: number } }) =>
      usersApi.toggleUserLock(userId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.detail(variables.userId) });
      toast.success(variables.data.is_locked ? 'User locked successfully' : 'User unlocked successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update user lock status');
    },
  });
};

// Bulk update users mutation
export const useBulkUpdateUsers = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userIds, data }: { userIds: number[]; data: Partial<User> }) =>
      usersApi.bulkUpdateUsers(userIds, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      // Invalidate specific user details
      variables.userIds.forEach(userId => {
        queryClient.invalidateQueries({ queryKey: userKeys.detail(userId) });
      });
      toast.success(`${variables.userIds.length} users updated successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update users');
    },
  });
};

// Unlock user mutation
export const useUnlockUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: number) => usersApi.unlockUser(userId),
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.detail(userId) });
      toast.success('User unlocked successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to unlock user');
    },
  });
};
